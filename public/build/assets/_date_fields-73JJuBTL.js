var l=document.getElementById("datepicker").getAttribute("data-lang");switch(l){case"el":l="gr";break;case"fr":l="fr";break;case"it":l="it";break;case"de":l="de";break;case"ru":l="ru";break;default:l="default";break}function k(){let n=new Date().fp_incr(2).getTime();if(Cookies.get("pickup_date")){let r=Cookies.get("pickup_date"),[t,e,o]=r.split("-").map(Number),d=new Date(o,e-1,t).getTime();if(isNaN(d))console.error("Invalid date format for 'pickup_date' cookie:",r);else if(d>n)return new Date(d)}return new Date(n)}function y(){if(Cookies.get("pickup_date")){let r=Cookies.get("pickup_date"),[t,e,o]=r.split("-").map(Number),d=new Date(o,e-1,t).getTime();if(Cookies.get("dropoff_date")==null)var n=new Date(o,e-1,t).fp_incr(7).getTime();else var n=new Date(d).fp_incr(3).getTime();if(Cookies.get("dropoff_date")){let i=Cookies.get("dropoff_date"),[a,s,c]=i.split("-").map(Number),f=new Date(c,s-1,a).getTime();return f<=n?new Date(n):new Date(f)}return new Date(n)}else return new Date().fp_incr(9)}function h(){return screen.width<1020?15:screen.width<1220?3:4}window.initFlatpickr=function(){window.fpInstance||(window.fpInstance=flatpickr("#datepicker",{mode:"range",inline:!0,showMonths:h(),minDate:new Date().fp_incr(2),dateFormat:"d-m-Y",defaultDate:[k(),y()],locale:l,onReady:function(n,r,t){p(n,r,t),Cookies.set("pickup_date",this.formatDate(k(),"d-m-Y")),Cookies.set("dropoff_date",this.formatDate(y(),"d-m-Y"))},onChange:function(n,r,t){if(p(n,r,t),!(this.selectedDates.length<2)){var e=this.selectedDates[0].getTime(),o=this.selectedDates[1].getTime(),d=(o-e)/864e5;if(d<3){var i=this.formatDate(new Date(e+2592e5),"d-m-Y");Cookies.set("dropoff_date",i),document.getElementById("dropoff_date").value=i,this.setDate([this.selectedDates[0],i])}}},onClose:function(n,r,t){if(p(n,r,t),this.selectedDates.length>0&&this.selectedDates[0]){var e=this.formatDate(this.selectedDates[0],"d-m-Y");Cookies.set("pickup_date",e),document.getElementById("pickup_date").textContent=e;var o=this.formatDate(this.selectedDates[1],"d-m-Y");Cookies.set("dropoff_date",o),document.getElementById("modal-pickup-date").textContent=e,document.getElementById("modal-dropoff-date").textContent=o,document.getElementById("pickup_date").value=e,document.getElementById("dropoff_date").value=o,document.getElementById("dates-calendar").style.display="none",document.getElementById("hours-list").style.display="block",document.getElementById("confirm-hours").style.display="block";var d=document.getElementsByClassName("inRange"),i=d.length+1;const a=document.querySelector(".total-rent-days");a&&(a.textContent=i)}}}))};document.querySelector(".flatpickr-calendar.inline");document.querySelectorAll("#pickup_date, #dropoff_date");function p(n,r,t){if(window.innerWidth>768)return;const e=t.calendarContainer;if(e.querySelectorAll(".flatpickr-months").forEach(a=>a.remove()),e.querySelectorAll(".flatpickr-weekdays:not(.single-weekdays)").forEach(a=>a.remove()),!e.querySelector(".single-weekdays")){const a=document.createElement("div");a.className="flatpickr-weekdays single-weekdays";const s=document.createElement("div");s.className="flatpickr-weekdaycontainer";const c=t.l10n&&t.l10n.weekdays&&t.l10n.weekdays.shorthand?t.l10n.weekdays.shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];for(let m=0;m<7;m++){const u=document.createElement("span");u.className="flatpickr-weekday",u.textContent=c[m],s.appendChild(u)}a.appendChild(s);const f=e.querySelector(".flatpickr-innerContainer");f&&e.insertBefore(a,f)}e.querySelectorAll(".fp-month-label").forEach(a=>a.remove());const o=e.querySelectorAll(".dayContainer"),d=t.currentMonth,i=t.currentYear;o.forEach((a,s)=>{var u;const c=document.createElement("div");c.className="fp-month-label";const f=new Date(i,d+s,1),m=((u=t.config)==null?void 0:u.lang)||"default";c.textContent=f.toLocaleDateString(m,{month:"long",year:"numeric"}),a.parentNode.insertBefore(c,a)})}

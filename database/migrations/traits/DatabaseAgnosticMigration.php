<?php

namespace Database\Migrations\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

trait DatabaseAgnosticMigration
{
    /**
     * Check if we're running on SQLite (typically for testing)
     */
    protected function isSQLite(): bool
    {
        return DB::getDriverName() === 'sqlite';
    }

    /**
     * Check if we're running on MySQL/MariaDB
     */
    protected function isMySQL(): bool
    {
        return in_array(DB::getDriverName(), ['mysql', 'mariadb']);
    }

    /**
     * Add or modify an ENUM column in a database-agnostic way
     */
    protected function addOrModifyEnum(string $table, string $column, array $values, string $default = null): void
    {
        if ($this->isSQLite()) {
            // SQLite: Use string column with validation in the model
            if (!Schema::hasColumn($table, $column)) {
                Schema::table($table, function (Blueprint $blueprint) use ($column, $default) {
                    $col = $blueprint->string($column, 10);
                    if ($default) {
                        $col->default($default);
                    }
                });
            }
        } else {
            // MySQL: Use native ENUM
            $enumValues = "'" . implode("', '", $values) . "'";
            $defaultClause = $default ? " DEFAULT '$default'" : '';
            
            if (Schema::hasColumn($table, $column)) {
                DB::statement("ALTER TABLE $table MODIFY COLUMN $column ENUM($enumValues)$defaultClause");
            } else {
                DB::statement("ALTER TABLE $table ADD COLUMN $column ENUM($enumValues)$defaultClause");
            }
        }
    }

    /**
     * Drop an ENUM column in a database-agnostic way
     */
    protected function dropEnum(string $table, string $column): void
    {
        if (Schema::hasColumn($table, $column)) {
            Schema::table($table, function (Blueprint $blueprint) use ($column) {
                $blueprint->dropColumn($column);
            });
        }
    }

    /**
     * Add a foreign key constraint in a database-agnostic way
     */
    protected function addForeignKey(string $table, string $column, string $referencedTable, string $referencedColumn = 'id', string $onDelete = 'cascade'): void
    {
        if ($this->isSQLite()) {
            // SQLite has limited foreign key support in migrations
            // We'll add the column but skip the constraint for testing
            if (!Schema::hasColumn($table, $column)) {
                Schema::table($table, function (Blueprint $blueprint) use ($column) {
                    $blueprint->unsignedBigInteger($column)->nullable();
                });
            }
        } else {
            // MySQL: Full foreign key support
            Schema::table($table, function (Blueprint $blueprint) use ($column, $referencedTable, $referencedColumn, $onDelete) {
                if (!Schema::hasColumn($table, $column)) {
                    $blueprint->unsignedBigInteger($column)->nullable();
                }
                $blueprint->foreign($column)->references($referencedColumn)->on($referencedTable)->onDelete($onDelete);
            });
        }
    }

    /**
     * Execute raw SQL only for specific database drivers
     */
    protected function executeForDriver(string $driver, string $sql): void
    {
        if (DB::getDriverName() === $driver) {
            DB::statement($sql);
        }
    }

    /**
     * Execute different SQL based on the database driver
     */
    protected function executeByDriver(array $driverSqlMap): void
    {
        $currentDriver = DB::getDriverName();
        
        if (isset($driverSqlMap[$currentDriver])) {
            DB::statement($driverSqlMap[$currentDriver]);
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateLanguageFieldInPostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check database driver for compatibility
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite: Use string column (ENUM not supported)
            if (!Schema::hasColumn('posts', 'language')) {
                Schema::table('posts', function (Blueprint $table) {
                    $table->string('language', 10)->default('en');
                });
            } else {
                // Column exists, ensure it has the right default
                DB::statement("UPDATE posts SET language = 'en' WHERE language IS NULL OR language = ''");
            }
        } else {
            // MySQL/MariaDB: Use ENUM
            try {
                DB::statement("ALTER TABLE posts MODIFY COLUMN language ENUM('en', 'el', 'de', 'fr', 'it', 'ru') DEFAULT 'en'");
            } catch (\Exception $e) {
                // If MODIFY fails, try ADD (column might not exist)
                if (!Schema::hasColumn('posts', 'language')) {
                    Schema::table('posts', function (Blueprint $table) {
                        $table->enum('language', ['en', 'el', 'de', 'fr', 'it', 'ru'])->default('en');
                    });
                } else {
                    throw $e;
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('posts', function (Blueprint $table) {
            //
        });
    }
}

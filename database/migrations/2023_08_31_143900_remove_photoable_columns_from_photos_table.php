<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class RemovePhotoableColumnsFromPhotosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check database driver for SQLite compatibility
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite: Drop columns one at a time
            if (Schema::hasColumn('photos', 'photoable_id')) {
                Schema::table('photos', function (Blueprint $table) {
                    $table->dropColumn('photoable_id');
                });
            }

            if (Schema::hasColumn('photos', 'photoable_type')) {
                Schema::table('photos', function (Blueprint $table) {
                    $table->dropColumn('photoable_type');
                });
            }
        } else {
            // MySQL: Can drop multiple columns at once
            Schema::table('photos', function (Blueprint $table) {
                if (Schema::hasColumn('photos', 'photoable_id')) {
                    $table->dropColumn('photoable_id');
                }
                if (Schema::hasColumn('photos', 'photoable_type')) {
                    $table->dropColumn('photoable_type');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('photos', function (Blueprint $table) {
            $table->unsignedBigInteger('photoable_id');
            $table->string('photoable_type');
        });
    }
}

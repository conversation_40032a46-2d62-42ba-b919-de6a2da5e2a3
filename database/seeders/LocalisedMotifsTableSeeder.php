<?php

namespace Database\Seeders;

use App\LocalisedMotif;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocalisedMotifsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('localised_motifs')->delete();

        $motifs = [
            [
                'title' => 'Heraklion',
            ],
            [
                'title' => 'Heraklion Airport',
            ],
            [
                'title' => 'Chania',
            ],
            [
                'title' => 'Chania Airport',
            ],
            [
                'title' => 'Rethymno',
            ],
            [
                'title' => 'Agios Nikolaos',
            ]
        ];

        foreach ($motifs as $motif) {
            LocalisedMotif::create([
                'title' => $motif['title'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        $this->command->info('Localised motifs table seeded!');
    }
}

<?php

namespace Database\Factories;

use App\Post;
use App\PostTranslation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\PostTranslation>
 */
class PostTranslationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PostTranslation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(4);
        
        return [
            'post_id' => Post::factory(),
            'locale' => 'en',
            'title' => $title,
            'meta_title' => $title,
            'slug' => Str::slug($title) . '-' . $this->faker->unique()->numberBetween(1, 10000),
            'content' => $this->faker->paragraphs(3, true),
            'meta_description' => $this->faker->sentence(10),
        ];
    }

    /**
     * Create a Greek translation.
     */
    public function greek(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'el',
        ]);
    }

    /**
     * Create an English translation.
     */
    public function english(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'en',
        ]);
    }

    /**
     * Create a translation with custom locale.
     */
    public function locale(string $locale): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => $locale,
        ]);
    }

    /**
     * Create a translation with custom title.
     */
    public function withTitle(string $title): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => $title,
            'meta_title' => $title,
            'slug' => Str::slug($title) . '-' . $this->faker->unique()->numberBetween(1, 10000),
        ]);
    }

    /**
     * Create a translation for a specific post.
     */
    public function forPost(Post $post): static
    {
        return $this->state(fn (array $attributes) => [
            'post_id' => $post->id,
        ]);
    }
}

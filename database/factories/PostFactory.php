<?php

namespace Database\Factories;

use App\Post;
use App\PostTranslation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Post>
 */
class PostFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Post::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'published' => $this->faker->boolean(70), // 70% chance of being published
            'featured' => $this->faker->boolean(20),  // 20% chance of being featured
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (Post $post) {
            // Create English translation (default)
            $this->createTranslation($post, 'en');
            
            // Sometimes create Greek translation too
            if ($this->faker->boolean(30)) {
                $this->createTranslation($post, 'el');
            }
        });
    }

    /**
     * Create a translation for the post.
     */
    private function createTranslation(Post $post, string $locale): void
    {
        $title = $this->faker->sentence(4);
        
        PostTranslation::create([
            'post_id' => $post->id,
            'locale' => $locale,
            'title' => $title,
            'meta_title' => $title,
            'slug' => Str::slug($title) . '-' . $post->id,
            'content' => $this->faker->paragraphs(3, true),
            'meta_description' => $this->faker->sentence(10),
        ]);
    }

    /**
     * Indicate that the post should be published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'published' => true,
        ]);
    }

    /**
     * Indicate that the post should be unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'published' => false,
        ]);
    }

    /**
     * Indicate that the post should be featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
        ]);
    }

    /**
     * Indicate that the post should not be featured.
     */
    public function notFeatured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => false,
        ]);
    }

    /**
     * Create a post with only English translation.
     */
    public function englishOnly(): static
    {
        return $this->afterCreating(function (Post $post) {
            // Clear any existing translations
            $post->translations()->delete();
            
            // Create only English translation
            $this->createTranslation($post, 'en');
        });
    }

    /**
     * Create a post with both English and Greek translations.
     */
    public function bilingual(): static
    {
        return $this->afterCreating(function (Post $post) {
            // Clear any existing translations
            $post->translations()->delete();
            
            // Create both translations
            $this->createTranslation($post, 'en');
            $this->createTranslation($post, 'el');
        });
    }

    /**
     * Create a post with custom title (for testing).
     */
    public function withTitle(string $title, string $locale = 'en'): static
    {
        return $this->afterCreating(function (Post $post) use ($title, $locale) {
            // Clear existing translations for this locale
            $post->translations()->where('locale', $locale)->delete();
            
            // Create translation with custom title
            PostTranslation::create([
                'post_id' => $post->id,
                'locale' => $locale,
                'title' => $title,
                'meta_title' => $title,
                'slug' => Str::slug($title) . '-' . $post->id,
                'content' => $this->faker->paragraphs(3, true),
                'meta_description' => $this->faker->sentence(10),
            ]);
        });
    }
}

<?php

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| Here you may define all of your model factories. Model factories give
| you a convenient way to create models for testing and seeding your
| database. Just tell the factory how a default model should look.
|
*/

$factory->define(App\User::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->name,
        'email' => $faker->safeEmail,
        'password' => bcrypt(str_random(10)),
        'remember_token' => str_random(10),
    ];
});

$factory->define(App\Post::class, function (Faker\Generator $faker) {
    return [
        'published' => $faker->boolean(70), // 70% chance of being published
        'featured' => $faker->boolean(20),  // 20% chance of being featured
    ];
});

$factory->afterCreating(App\Post::class, function ($post, $faker) {
    // Create English translation
    $title = $faker->sentence(4);
    App\PostTranslation::create([
        'post_id' => $post->id,
        'locale' => 'en',
        'title' => $title,
        'meta_title' => $title,
        'slug' => Illuminate\Support\Str::slug($title) . '-' . $post->id,
        'content' => $faker->paragraphs(3, true),
        'meta_description' => $faker->sentence(10),
    ]);
});

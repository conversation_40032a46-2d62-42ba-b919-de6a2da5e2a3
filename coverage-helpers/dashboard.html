<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Accessory.php.html#8">App\Accessory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#6">App\BaseModel</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Category.php.html#7">App\Category</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#6">App\Coupon</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Customer.php.html#8">App\Customer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Feedback.php.html#9">App\Feedback</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helpers/ChangePortal.php.html#9">App\Helpers\ChangePortal</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helpers/TimeRange.php.html#4">App\Helpers\TimeRange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#10">App\Http\Controllers\AccessoriesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#11">App\Http\Controllers\Auth\LoginController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#9">App\Http\Controllers\Auth\TwoFactorController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#10">App\Http\Controllers\AuthController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#10">App\Http\Controllers\BookingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#10">App\Http\Controllers\CategoriesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#20">App\Http\Controllers\ContactController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#24">App\Http\Controllers\Controller</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#12">App\Http\Controllers\CouponsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CustomersController.php.html#8">App\Http\Controllers\CustomersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/DashboardController.php.html#9">App\Http\Controllers\DashboardController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedController.php.html#8">App\Http\Controllers\FeedController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#13">App\Http\Controllers\FeedbackController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/HomeController.php.html#9">App\Http\Controllers\HomeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#12">App\Http\Controllers\ImageController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#20">App\Http\Controllers\ListingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#10">App\Http\Controllers\LocationsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/OffersController.php.html#14">App\Http\Controllers\OffersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#8">App\Http\Controllers\PagesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#12">App\Http\Controllers\PlacesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#11">App\Http\Controllers\PopupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#14">App\Http\Controllers\PostsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#13">App\Http\Controllers\PricingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#18">App\Http\Controllers\QuotesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#15">App\Http\Controllers\RepeatingClientsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#20">App\Http\Controllers\ReservationsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReviewsController.php.html#5">App\Http\Controllers\ReviewsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RobotsController.php.html#4">App\Http\Controllers\RobotsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SearchController.php.html#10">App\Http\Controllers\SearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SessionsController.php.html#7">App\Http\Controllers\SessionsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/TestController.php.html#9">App\Http\Controllers\TestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/AvailabilityRequest.php.html#7">App\Http\Requests\AvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#7">App\Http\Requests\ContactRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/PricingRequest.php.html#7">App\Http\Requests\PricingRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#7">App\Http\Requests\ReservationRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Image.php.html#8">App\Image</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendDuplicateReservationReminder.php.html#14">App\Jobs\SendDuplicateReservationReminder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendOfferNotification.php.html#16">App\Jobs\SendOfferNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationFeedback.php.html#21">App\Jobs\SendReservationFeedback</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationReminder.php.html#23">App\Jobs\SendReservationReminder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationVerifyReminder.php.html#21">App\Jobs\SendReservationVerifyReminder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Listing.php.html#22">App\Listing</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Livewire/Frontend/ReviewsPage.php.html#10">App\Livewire\Frontend\ReviewsPage</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#11">App\Location</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Models/User.php.html#11">App\Models\User</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/TwoFactorCode.php.html#10">App\Notifications\TwoFactorCode</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Offer.php.html#8">App\Offer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Popup.php.html#8">App\Popup</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Post.php.html#23">App\Post</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslation.php.html#12">App\PostTranslation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pricing.php.html#9">App\Pricing</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/BroadcastServiceProvider.php.html#8">App\Providers\BroadcastServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/BusServiceProvider.php.html#6">App\Providers\BusServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ConfigServiceProvider.php.html#5">App\Providers\ConfigServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#12">App\Quote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RepeatingClient.php.html#5">App\RepeatingClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#6">App\Repositories\Eloquent\Repository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#15">App\Repositories\ImageRepository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#11">App\Repositories\ListingRepository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/PeriodRepository.php.html#10">App\Repositories\PeriodRepository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#19">App\Reservation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Rules/InvisibleRecaptchaRule.php.html#9">App\Rules\InvisibleRecaptchaRule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#16">App\Services\Offer\BasicDiscount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/CouponDiscount.php.html#14">App\Services\Offer\CouponDiscount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#16">App\Services\Offer\DateRange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/PredefinedOffers.php.html#18">App\Services\Offer\PredefinedOffers</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#20">App\Services\Offer\RentalOffer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#16">App\Services\Offer\StaticDiscount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ContactLeadSearch.php.html#8">App\Services\Search\ContactLeadSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/CustomerSearch.php.html#8">App\Services\Search\CustomerSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/AuthorShownName.php.html#7">App\Services\Search\Filters\AuthorShownName</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/AuthorTitle.php.html#7">App\Services\Search\Filters\AuthorTitle</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/CreatedAfter.php.html#7">App\Services\Search\Filters\CreatedAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/CreatedBefore.php.html#7">App\Services\Search\Filters\CreatedBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Email.php.html#7">App\Services\Search\Filters\Email</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Enabled.php.html#7">App\Services\Search\Filters\Enabled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Featured.php.html#7">App\Services\Search\Filters\Featured</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#7">App\Services\Search\Filters\GlobalFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GroupId.php.html#7">App\Services\Search\Filters\GroupId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasCategories.php.html#7">App\Services\Search\Filters\HasCategories</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasLocation.php.html#7">App\Services\Search\Filters\HasLocation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasMotifs.php.html#7">App\Services\Search\Filters\HasMotifs</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasReservation.php.html#7">App\Services\Search\Filters\HasReservation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasTags.php.html#7">App\Services\Search\Filters\HasTags</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Id.php.html#7">App\Services\Search\Filters\Id</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Intercepted.php.html#7">App\Services\Search\Filters\Intercepted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Language.php.html#7">App\Services\Search\Filters\Language</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/LocationId.php.html#7">App\Services\Search\Filters\LocationId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Manufacturer.php.html#7">App\Services\Search\Filters\Manufacturer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Model.php.html#7">App\Services\Search\Filters\Model</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/MotifRelatedId.php.html#7">App\Services\Search\Filters\MotifRelatedId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Name.php.html#7">App\Services\Search\Filters\Name</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/OfferId.php.html#7">App\Services\Search\Filters\OfferId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Origin.php.html#7">App\Services\Search\Filters\Origin</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PickupAfter.php.html#7">App\Services\Search\Filters\PickupAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PickupBefore.php.html#7">App\Services\Search\Filters\PickupBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Published.php.html#7">App\Services\Search\Filters\Published</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PublishedCretanrentals.php.html#7">App\Services\Search\Filters\PublishedCretanrentals</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PublishedEurodollar.php.html#7">App\Services\Search\Filters\PublishedEurodollar</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReservationId.php.html#7">App\Services\Search\Filters\ReservationId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewId.php.html#7">App\Services\Search\Filters\ReviewId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewRating.php.html#7">App\Services\Search\Filters\ReviewRating</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewText.php.html#7">App\Services\Search\Filters\ReviewText</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Sent.php.html#7">App\Services\Search\Filters\Sent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Show.php.html#7">App\Services\Search\Filters\Show</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ShowVerified.php.html#7">App\Services\Search\Filters\ShowVerified</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Site.php.html#7">App\Services\Search\Filters\Site</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Testimonialised.php.html#7">App\Services\Search\Filters\Testimonialised</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Title.php.html#7">App\Services\Search\Filters\Title</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Translated.php.html#7">App\Services\Search\Filters\Translated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/TranslationTitle.php.html#7">App\Services\Search\Filters\TranslationTitle</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/UpdatedAfter.php.html#7">App\Services\Search\Filters\UpdatedAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/UpdatedBefore.php.html#7">App\Services\Search\Filters\UpdatedBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/GoogleReviewSearch.php.html#8">App\Services\Search\GoogleReviewSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ListingSearch.php.html#8">App\Services\Search\ListingSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/OfferSearch.php.html#8">App\Services\Search\OfferSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/PostSearch.php.html#8">App\Services\Search\PostSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ReservationSearch.php.html#8">App\Services\Search\ReservationSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#9">App\Services\Search\Search</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/AccessoryValidator.php.html#13">App\Services\Validation\AccessoryValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/CouponValidator.php.html#7">App\Services\Validation\CouponValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/LocationValidator.php.html#13">App\Services\Validation\LocationValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/ReservationValidator.php.html#13">App\Services\Validation\ReservationValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/ValidationException.php.html#5">App\Services\Validation\ValidationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#5">App\Services\Validation\Validator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Supergroup.php.html#10">App\Supergroup</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag.php.html#9">App\Tag</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/BlogDetailsComposer.php.html#9">App\View\Composers\BlogDetailsComposer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/FaqPartialComposer.php.html#10">App\View\Composers\FaqPartialComposer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/SearchBucketComposer.php.html#11">App\View\Composers\SearchBucketComposer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/TestimonialPartialComposer.php.html#11">App\View\Composers\TestimonialPartialComposer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ImageHandling/ImageableTrait.php.html#17">App\Services\ImageHandling\ImageableTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PhotoHandling/PhotoableTrait.php.html#7">App\Services\PhotoHandling\PhotoableTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/CustomerAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\CustomerAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/FeedbackAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\FeedbackAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\GroupAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\ImageAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#9">App\Traits\AccessorsMutators\ListingAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\LocationAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/OfferAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\OfferAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PhotoAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\PhotoAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#9">App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#8">App\Traits\AccessorsMutators\QuoteAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#10">App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#11">App\Traits\AccessorsMutators\ReservationAccessorsMutators</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/CategoryRelationships.php.html#5">App\Traits\Relationships\CategoryRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/CustomerRelationships.php.html#5">App\Traits\Relationships\CustomerRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/FeedbackRelationships.php.html#5">App\Traits\Relationships\FeedbackRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GoogleReviewRelationships.php.html#9">App\Traits\Relationships\GoogleReviewRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GroupRelationships.php.html#8">App\Traits\Relationships\GroupRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ImageRelationships.php.html#5">App\Traits\Relationships\ImageRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LanguageRelationships.php.html#7">App\Traits\Relationships\LanguageRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#12">App\Traits\Relationships\ListingRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocalisedMotifRelationships.php.html#8">App\Traits\Relationships\LocalisedMotifRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#7">App\Traits\Relationships\LocationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/MotifRelationships.php.html#10">App\Traits\Relationships\MotifRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#14">App\Traits\Relationships\OfferRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PeriodRelationships.php.html#8">App\Traits\Relationships\PeriodRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#12">App\Traits\Relationships\PostRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostTranslationRelationships.php.html#7">App\Traits\Relationships\PostTranslationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/QuoteRelationships.php.html#5">App\Traits\Relationships\QuoteRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ReservationRelationships.php.html#9">App\Traits\Relationships\ReservationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/SupergroupRelationships.php.html#10">App\Traits\Relationships\SupergroupRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/TagRelationships.php.html#8">App\Traits\Relationships\TagRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/GoogleReviewScopes.php.html#5">App\Traits\Scopes\GoogleReviewScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/ListingScopes.php.html#5">App\Traits\Scopes\ListingScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#5">App\Traits\Scopes\LocalisableScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/PostScopes.php.html#5">App\Traits\Scopes\PostScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/ReservationScopes.php.html#5">App\Traits\Scopes\ReservationScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/AuthServiceProvider.php.html#8">App\Providers\AuthServiceProvider</a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Providers/AppServiceProvider.php.html#9">App\Providers\AppServiceProvider</a></td><td class="text-right">72%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#20">App\Services\Offer\RentalOffer</a></td><td class="text-right">3782</td></tr>
       <tr><td><a href="Reservation.php.html#19">App\Reservation</a></td><td class="text-right">1722</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#20">App\Http\Controllers\ListingsController</a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#20">App\Http\Controllers\ReservationsController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#12">App\Http\Controllers\PlacesController</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#11">App\Repositories\ListingRepository</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#10">App\Http\Controllers\BookingsController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#15">App\Repositories\ImageRepository</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#16">App\Services\Offer\DateRange</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#24">App\Http\Controllers\Controller</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#7">App\Services\Search\Filters\GlobalFilter</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#10">App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#12">App\Http\Controllers\ImageController</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#13">App\Http\Controllers\FeedbackController</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Location.php.html#11">App\Location</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#10">App\Http\Controllers\CategoriesController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Offer.php.html#8">App\Offer</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#6">App\Repositories\Eloquent\Repository</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#16">App\Services\Offer\BasicDiscount</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#12">App\Traits\Relationships\PostRelationships</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#12">App\Http\Controllers\CouponsController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#8">App\Http\Controllers\PagesController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#11">App\Http\Controllers\PopupController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#16">App\Services\Offer\StaticDiscount</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#14">App\Http\Controllers\PostsController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#13">App\Http\Controllers\PricingController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#15">App\Http\Controllers\RepeatingClientsController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#10">App\Http\Controllers\AccessoriesController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#10">App\Http\Controllers\LocationsController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#18">App\Http\Controllers\QuotesController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#7">App\Http\Requests\ReservationRequest</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Quote.php.html#12">App\Quote</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Services/Offer/PredefinedOffers.php.html#18">App\Services\Offer\PredefinedOffers</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\GroupAccessorsMutators</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#20">App\Http\Controllers\ContactController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#8">App\Traits\AccessorsMutators\QuoteAccessorsMutators</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#9">App\Traits\AccessorsMutators\ListingAccessorsMutators</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#11">App\Traits\AccessorsMutators\ReservationAccessorsMutators</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Image.php.html#8">App\Image</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Jobs/SendReservationFeedback.php.html#21">App\Jobs\SendReservationFeedback</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PhotoAccessorsMutators.php.html#5">App\Traits\AccessorsMutators\PhotoAccessorsMutators</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Category.php.html#7">App\Category</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#7">App\Http\Requests\ContactRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#9">App\Services\Search\Search</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Accessory.php.html#8">App\Accessory</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#11">App\Http\Controllers\Auth\LoginController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#9">App\Http\Controllers\Auth\TwoFactorController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#10">App\Http\Controllers\AuthController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/FeedController.php.html#8">App\Http\Controllers\FeedController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Popup.php.html#8">App\Popup</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="View/Composers/BlogDetailsComposer.php.html#9">App\View\Composers\BlogDetailsComposer</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\ImageAccessorsMutators</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\LocationAccessorsMutators</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Jobs/SendReservationReminder.php.html#23">App\Jobs\SendReservationReminder</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Livewire/Frontend/ReviewsPage.php.html#10">App\Livewire\Frontend\ReviewsPage</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/CouponDiscount.php.html#14">App\Services\Offer\CouponDiscount</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#5">App\Services\Validation\Validator</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="View/Composers/SearchBucketComposer.php.html#11">App\View\Composers\SearchBucketComposer</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/ImageHandling/ImageableTrait.php.html#17">App\Services\ImageHandling\ImageableTrait</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#7">App\Traits\Relationships\LocationRelationships</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Helpers/ChangePortal.php.html#9">App\Helpers\ChangePortal</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Helpers/TimeRange.php.html#4">App\Helpers\TimeRange</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/HomeController.php.html#9">App\Http\Controllers\HomeController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/OffersController.php.html#14">App\Http\Controllers\OffersController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/SearchController.php.html#10">App\Http\Controllers\SearchController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Jobs/SendOfferNotification.php.html#16">App\Jobs\SendOfferNotification</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Jobs/SendReservationVerifyReminder.php.html#21">App\Jobs\SendReservationVerifyReminder</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RepeatingClient.php.html#5">App\RepeatingClient</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/CustomerAccessorsMutators.php.html#7">App\Traits\AccessorsMutators\CustomerAccessorsMutators</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/CustomersController.php.html#8">App\Http\Controllers\CustomersController</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Rules/InvisibleRecaptchaRule.php.html#9">App\Rules\InvisibleRecaptchaRule</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Validation/AccessoryValidator.php.html#13">App\Services\Validation\AccessoryValidator</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Validation/LocationValidator.php.html#13">App\Services\Validation\LocationValidator</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="View/Composers/FaqPartialComposer.php.html#10">App\View\Composers\FaqPartialComposer</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Accessory.php.html#28"><abbr title="App\Accessory::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Accessory.php.html#45"><abbr title="App\Accessory::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Accessory.php.html#60"><abbr title="App\Accessory::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#14"><abbr title="App\BaseModel::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#36"><abbr title="App\BaseModel::validateAndUpdate">validateAndUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#47"><abbr title="App\BaseModel::performCreationValidation">performCreationValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#65"><abbr title="App\BaseModel::performUpdateValidation">performUpdateValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#85"><abbr title="App\BaseModel::performParentUpdate">performParentUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#99"><abbr title="App\BaseModel::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#109"><abbr title="App\BaseModel::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseModel.php.html#119"><abbr title="App\BaseModel::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Category.php.html#22"><abbr title="App\Category::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Category.php.html#33"><abbr title="App\Category::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Category.php.html#47"><abbr title="App\Category::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Category.php.html#58"><abbr title="App\Category::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#28"><abbr title="App\Coupon::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#38"><abbr title="App\Coupon::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#49"><abbr title="App\Coupon::getDiscountPercentageAttribute">getDiscountPercentageAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#63"><abbr title="App\Coupon::scopeEurodollarValid">scopeEurodollarValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#75"><abbr title="App\Coupon::scopeEurodollarPromoted">scopeEurodollarPromoted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#88"><abbr title="App\Coupon::scopeCretanrentalsValid">scopeCretanrentalsValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Coupon.php.html#100"><abbr title="App\Coupon::scopeCretanrentalsPromoted">scopeCretanrentalsPromoted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Customer.php.html#29"><abbr title="App\Customer::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Customer.php.html#40"><abbr title="App\Customer::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Customer.php.html#50"><abbr title="App\Customer::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Feedback.php.html#31"><abbr title="App\Feedback::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Feedback.php.html#84"><abbr title="App\Feedback::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helpers/ChangePortal.php.html#11"><abbr title="App\Helpers\ChangePortal::LoadPortal">LoadPortal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helpers/TimeRange.php.html#14"><abbr title="App\Helpers\TimeRange::create_time_range">create_time_range</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#17"><abbr title="App\Http\Controllers\AccessoriesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#32"><abbr title="App\Http\Controllers\AccessoriesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#43"><abbr title="App\Http\Controllers\AccessoriesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#70"><abbr title="App\Http\Controllers\AccessoriesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#87"><abbr title="App\Http\Controllers\AccessoriesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#114"><abbr title="App\Http\Controllers\AccessoriesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#38"><abbr title="App\Http\Controllers\Auth\LoginController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#43"><abbr title="App\Http\Controllers\Auth\LoginController::username">username</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#48"><abbr title="App\Http\Controllers\Auth\LoginController::authenticated">authenticated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#16"><abbr title="App\Http\Controllers\Auth\TwoFactorController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#21"><abbr title="App\Http\Controllers\Auth\TwoFactorController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#26"><abbr title="App\Http\Controllers\Auth\TwoFactorController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#46"><abbr title="App\Http\Controllers\Auth\TwoFactorController::resend">resend</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#15"><abbr title="App\Http\Controllers\AuthController::showLogin">showLogin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#30"><abbr title="App\Http\Controllers\AuthController::handleLogin">handleLogin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#17"><abbr title="App\Http\Controllers\BookingsController::getCurrentReservationData">getCurrentReservationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#34"><abbr title="App\Http\Controllers\BookingsController::showBooking">showBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#57"><abbr title="App\Http\Controllers\BookingsController::handleBooking">handleBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#80"><abbr title="App\Http\Controllers\BookingsController::fleetIndex">fleetIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#172"><abbr title="App\Http\Controllers\BookingsController::initialSetupOfCookie">initialSetupOfCookie</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#17"><abbr title="App\Http\Controllers\CategoriesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#33"><abbr title="App\Http\Controllers\CategoriesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#46"><abbr title="App\Http\Controllers\CategoriesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#71"><abbr title="App\Http\Controllers\CategoriesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#86"><abbr title="App\Http\Controllers\CategoriesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#116"><abbr title="App\Http\Controllers\CategoriesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#28"><abbr title="App\Http\Controllers\ContactController::showContact">showContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#73"><abbr title="App\Http\Controllers\ContactController::handleContact">handleContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#151"><abbr title="App\Http\Controllers\ContactController::handleNewsletter">handleNewsletter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#176"><abbr title="App\Http\Controllers\ContactController::getNewslettersIndex">getNewslettersIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#32"><abbr title="App\Http\Controllers\Controller::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#132"><abbr title="App\Http\Controllers\Controller::setupLayout">setupLayout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#149"><abbr title="App\Http\Controllers\Controller::overwriteOpengraph">overwriteOpengraph</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#165"><abbr title="App\Http\Controllers\Controller::popup">popup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#21"><abbr title="App\Http\Controllers\CouponsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#44"><abbr title="App\Http\Controllers\CouponsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#59"><abbr title="App\Http\Controllers\CouponsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#88"><abbr title="App\Http\Controllers\CouponsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#100"><abbr title="App\Http\Controllers\CouponsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#118"><abbr title="App\Http\Controllers\CouponsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#150"><abbr title="App\Http\Controllers\CouponsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/CustomersController.php.html#14"><abbr title="App\Http\Controllers\CustomersController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/DashboardController.php.html#16"><abbr title="App\Http\Controllers\DashboardController::getIndex">getIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedController.php.html#12"><abbr title="App\Http\Controllers\FeedController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedController.php.html#25"><abbr title="App\Http\Controllers\FeedController::exportCarsCsv">exportCarsCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#20"><abbr title="App\Http\Controllers\FeedbackController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#59"><abbr title="App\Http\Controllers\FeedbackController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#148"><abbr title="App\Http\Controllers\FeedbackController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#164"><abbr title="App\Http\Controllers\FeedbackController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/HomeController.php.html#12"><abbr title="App\Http\Controllers\HomeController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/HomeController.php.html#20"><abbr title="App\Http\Controllers\HomeController::getIndex">getIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#18"><abbr title="App\Http\Controllers\ImageController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#32"><abbr title="App\Http\Controllers\ImageController::postUpload">postUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#40"><abbr title="App\Http\Controllers\ImageController::deleteUpload">deleteUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#55"><abbr title="App\Http\Controllers\ImageController::getListingImages">getListingImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#86"><abbr title="App\Http\Controllers\ImageController::getLocationImages">getLocationImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#24"><abbr title="App\Http\Controllers\ListingsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#36"><abbr title="App\Http\Controllers\ListingsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#255"><abbr title="App\Http\Controllers\ListingsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#477"><abbr title="App\Http\Controllers\ListingsController::getPrice">getPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#550"><abbr title="App\Http\Controllers\ListingsController::handleUnavailable">handleUnavailable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#18"><abbr title="App\Http\Controllers\LocationsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#34"><abbr title="App\Http\Controllers\LocationsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#47"><abbr title="App\Http\Controllers\LocationsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#77"><abbr title="App\Http\Controllers\LocationsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#93"><abbr title="App\Http\Controllers\LocationsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#123"><abbr title="App\Http\Controllers\LocationsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/OffersController.php.html#23"><abbr title="App\Http\Controllers\OffersController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#10"><abbr title="App\Http\Controllers\PagesController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#21"><abbr title="App\Http\Controllers\PagesController::about">about</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#61"><abbr title="App\Http\Controllers\PagesController::offers">offers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#70"><abbr title="App\Http\Controllers\PagesController::crete">crete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#78"><abbr title="App\Http\Controllers\PagesController::services">services</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#118"><abbr title="App\Http\Controllers\PagesController::branches">branches</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#127"><abbr title="App\Http\Controllers\PagesController::faq">faq</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#189"><abbr title="App\Http\Controllers\PagesController::policy">policy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#229"><abbr title="App\Http\Controllers\PagesController::privacy">privacy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#260"><abbr title="App\Http\Controllers\PagesController::safety">safety</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#293"><abbr title="App\Http\Controllers\PagesController::insurance">insurance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#32"><abbr title="App\Http\Controllers\PlacesController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#74"><abbr title="App\Http\Controllers\PlacesController::heraklion">heraklion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#184"><abbr title="App\Http\Controllers\PlacesController::heraklion_airport">heraklion_airport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#298"><abbr title="App\Http\Controllers\PlacesController::chania_airport">chania_airport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#411"><abbr title="App\Http\Controllers\PlacesController::chania">chania</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#522"><abbr title="App\Http\Controllers\PlacesController::rethymno">rethymno</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#634"><abbr title="App\Http\Controllers\PlacesController::agios_nikolaos">agios_nikolaos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#749"><abbr title="App\Http\Controllers\PlacesController::setDefaultLocation">setDefaultLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#18"><abbr title="App\Http\Controllers\PopupController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#33"><abbr title="App\Http\Controllers\PopupController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#48"><abbr title="App\Http\Controllers\PopupController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#72"><abbr title="App\Http\Controllers\PopupController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#90"><abbr title="App\Http\Controllers\PopupController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#114"><abbr title="App\Http\Controllers\PopupController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#130"><abbr title="App\Http\Controllers\PopupController::return_bytes">return_bytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#16"><abbr title="App\Http\Controllers\PostsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#28"><abbr title="App\Http\Controllers\PostsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#37"><abbr title="App\Http\Controllers\PostsController::indexWithTags">indexWithTags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#48"><abbr title="App\Http\Controllers\PostsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#139"><abbr title="App\Http\Controllers\PostsController::handleIndex">handleIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#20"><abbr title="App\Http\Controllers\PricingController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#37"><abbr title="App\Http\Controllers\PricingController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#73"><abbr title="App\Http\Controllers\PricingController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#113"><abbr title="App\Http\Controllers\PricingController::availabilityUpdate">availabilityUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#26"><abbr title="App\Http\Controllers\QuotesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#47"><abbr title="App\Http\Controllers\QuotesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#57"><abbr title="App\Http\Controllers\QuotesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#112"><abbr title="App\Http\Controllers\QuotesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#132"><abbr title="App\Http\Controllers\QuotesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#156"><abbr title="App\Http\Controllers\QuotesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#22"><abbr title="App\Http\Controllers\RepeatingClientsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#44"><abbr title="App\Http\Controllers\RepeatingClientsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#55"><abbr title="App\Http\Controllers\RepeatingClientsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#95"><abbr title="App\Http\Controllers\RepeatingClientsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#114"><abbr title="App\Http\Controllers\RepeatingClientsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#126"><abbr title="App\Http\Controllers\RepeatingClientsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#151"><abbr title="App\Http\Controllers\RepeatingClientsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#27"><abbr title="App\Http\Controllers\ReservationsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#160"><abbr title="App\Http\Controllers\ReservationsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#171"><abbr title="App\Http\Controllers\ReservationsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#246"><abbr title="App\Http\Controllers\ReservationsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#266"><abbr title="App\Http\Controllers\ReservationsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#278"><abbr title="App\Http\Controllers\ReservationsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#299"><abbr title="App\Http\Controllers\ReservationsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#319"><abbr title="App\Http\Controllers\ReservationsController::confirm">confirm</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#343"><abbr title="App\Http\Controllers\ReservationsController::verify">verify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReviewsController.php.html#8"><abbr title="App\Http\Controllers\ReviewsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/ReviewsController.php.html#16"><abbr title="App\Http\Controllers\ReviewsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/RobotsController.php.html#6"><abbr title="App\Http\Controllers\RobotsController::robots">robots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SearchController.php.html#13"><abbr title="App\Http\Controllers\SearchController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SearchController.php.html#21"><abbr title="App\Http\Controllers\SearchController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SessionsController.php.html#14"><abbr title="App\Http\Controllers\SessionsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/SessionsController.php.html#28"><abbr title="App\Http\Controllers\SessionsController::destroyAll">destroyAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/TestController.php.html#12"><abbr title="App\Http\Controllers\TestController::offerEmail">offerEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/TestController.php.html#33"><abbr title="App\Http\Controllers\TestController::deepl">deepl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Controllers/TestController.php.html#48"><abbr title="App\Http\Controllers\TestController::multiMail">multiMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/AvailabilityRequest.php.html#14"><abbr title="App\Http\Requests\AvailabilityRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/AvailabilityRequest.php.html#24"><abbr title="App\Http\Requests\AvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#14"><abbr title="App\Http\Requests\ContactRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#24"><abbr title="App\Http\Requests\ContactRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#31"><abbr title="App\Http\Requests\ContactRequest::sanitize">sanitize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/PricingRequest.php.html#14"><abbr title="App\Http\Requests\PricingRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/PricingRequest.php.html#24"><abbr title="App\Http\Requests\PricingRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#14"><abbr title="App\Http\Requests\ReservationRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#24"><abbr title="App\Http\Requests\ReservationRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#31"><abbr title="App\Http\Requests\ReservationRequest::sanitize">sanitize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Image.php.html#33"><abbr title="App\Image::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Image.php.html#44"><abbr title="App\Image::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Image.php.html#62"><abbr title="App\Image::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendDuplicateReservationReminder.php.html#23"><abbr title="App\Jobs\SendDuplicateReservationReminder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendOfferNotification.php.html#30"><abbr title="App\Jobs\SendOfferNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendOfferNotification.php.html#40"><abbr title="App\Jobs\SendOfferNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationFeedback.php.html#32"><abbr title="App\Jobs\SendReservationFeedback::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationFeedback.php.html#42"><abbr title="App\Jobs\SendReservationFeedback::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationReminder.php.html#34"><abbr title="App\Jobs\SendReservationReminder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationReminder.php.html#44"><abbr title="App\Jobs\SendReservationReminder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationVerifyReminder.php.html#32"><abbr title="App\Jobs\SendReservationVerifyReminder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Jobs/SendReservationVerifyReminder.php.html#42"><abbr title="App\Jobs\SendReservationVerifyReminder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Listing.php.html#77"><abbr title="App\Listing::registerMediaConversions">registerMediaConversions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Listing.php.html#90"><abbr title="App\Listing::sluggable">sluggable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Listing.php.html#99"><abbr title="App\Listing::toSitemapTag">toSitemapTag</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Livewire/Frontend/ReviewsPage.php.html#19"><abbr title="App\Livewire\Frontend\ReviewsPage::loadMore">loadMore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Livewire/Frontend/ReviewsPage.php.html#24"><abbr title="App\Livewire\Frontend\ReviewsPage::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#43"><abbr title="App\Location::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#61"><abbr title="App\Location::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#88"><abbr title="App\Location::reviews">reviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#100"><abbr title="App\Location::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Location.php.html#119"><abbr title="App\Location::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Models/User.php.html#55"><abbr title="App\Models\User::generateTwoFactorCode">generateTwoFactorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Models/User.php.html#66"><abbr title="App\Models\User::resetTwoFactorCode">resetTwoFactorCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/TwoFactorCode.php.html#19"><abbr title="App\Notifications\TwoFactorCode::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/TwoFactorCode.php.html#30"><abbr title="App\Notifications\TwoFactorCode::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/TwoFactorCode.php.html#41"><abbr title="App\Notifications\TwoFactorCode::toMail">toMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/TwoFactorCode.php.html#58"><abbr title="App\Notifications\TwoFactorCode::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Offer.php.html#44"><abbr title="App\Offer::isReady">isReady</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Popup.php.html#26"><abbr title="App\Popup::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Popup.php.html#42"><abbr title="App\Popup::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Popup.php.html#58"><abbr title="App\Popup::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Post.php.html#47"><abbr title="App\Post::registerMediaConversions">registerMediaConversions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Post.php.html#57"><abbr title="App\Post::excerpt">excerpt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Post.php.html#68"><abbr title="App\Post::toSitemapTag">toSitemapTag</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslation.php.html#30"><abbr title="App\PostTranslation::sluggable">sluggable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslation.php.html#41"><abbr title="App\PostTranslation::getSearchResult">getSearchResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pricing.php.html#23"><abbr title="App\Pricing::booted">booted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/BroadcastServiceProvider.php.html#13"><abbr title="App\Providers\BroadcastServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/BusServiceProvider.php.html#14"><abbr title="App\Providers\BusServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/BusServiceProvider.php.html#29"><abbr title="App\Providers\BusServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/ConfigServiceProvider.php.html#16"><abbr title="App\Providers\ConfigServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#31"><abbr title="App\Quote::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#84"><abbr title="App\Quote::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RepeatingClient.php.html#25"><abbr title="App\RepeatingClient::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RepeatingClient.php.html#34"><abbr title="App\RepeatingClient::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#17"><abbr title="App\Repositories\Eloquent\Repository::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#26"><abbr title="App\Repositories\Eloquent\Repository::all">all</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#36"><abbr title="App\Repositories\Eloquent\Repository::lists">lists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#46"><abbr title="App\Repositories\Eloquent\Repository::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#55"><abbr title="App\Repositories\Eloquent\Repository::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#66"><abbr title="App\Repositories\Eloquent\Repository::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#77"><abbr title="App\Repositories\Eloquent\Repository::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#87"><abbr title="App\Repositories\Eloquent\Repository::find">find</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#98"><abbr title="App\Repositories\Eloquent\Repository::findBy">findBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#107"><abbr title="App\Repositories\Eloquent\Repository::orderBy">orderBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#121"><abbr title="App\Repositories\Eloquent\Repository::findOrFail">findOrFail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#133"><abbr title="App\Repositories\Eloquent\Repository::where">where</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#21"><abbr title="App\Repositories\ImageRepository::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#26"><abbr title="App\Repositories\ImageRepository::getConfigFullSize">getConfigFullSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#31"><abbr title="App\Repositories\ImageRepository::setConfigFullSize">setConfigFullSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#39"><abbr title="App\Repositories\ImageRepository::getConfigIconSize">getConfigIconSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#44"><abbr title="App\Repositories\ImageRepository::setConfigIconSize">setConfigIconSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#52"><abbr title="App\Repositories\ImageRepository::upload">upload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#103"><abbr title="App\Repositories\ImageRepository::createUniqueFilename">createUniqueFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#121"><abbr title="App\Repositories\ImageRepository::original">original</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#132"><abbr title="App\Repositories\ImageRepository::icon">icon</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#146"><abbr title="App\Repositories\ImageRepository::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#188"><abbr title="App\Repositories\ImageRepository::sanitize">sanitize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#13"><abbr title="App\Repositories\ListingRepository::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#22"><abbr title="App\Repositories\ListingRepository::findBySlug">findBySlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#34"><abbr title="App\Repositories\ListingRepository::findListings">findListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#50"><abbr title="App\Repositories\ListingRepository::getRelated">getRelated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#65"><abbr title="App\Repositories\ListingRepository::getSuggested">getSuggested</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#86"><abbr title="App\Repositories\ListingRepository::findByGroups">findByGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#102"><abbr title="App\Repositories\ListingRepository::findByTransmission">findByTransmission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#125"><abbr title="App\Repositories\ListingRepository::findByFuel">findByFuel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#148"><abbr title="App\Repositories\ListingRepository::findBySeats">findBySeats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#176"><abbr title="App\Repositories\ListingRepository::getListings">getListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Repositories/PeriodRepository.php.html#12"><abbr title="App\Repositories\PeriodRepository::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#64"><abbr title="App\Reservation::scopeUpcoming">scopeUpcoming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#74"><abbr title="App\Reservation::scopePending">scopePending</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#84"><abbr title="App\Reservation::scopeScheduled">scopeScheduled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#89"><abbr title="App\Reservation::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#380"><abbr title="App\Reservation::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#389"><abbr title="App\Reservation::shouldSendReminder">shouldSendReminder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#401"><abbr title="App\Reservation::handleCustomer">handleCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#427"><abbr title="App\Reservation::getReservationShowDataAggregatedBy">getReservationShowDataAggregatedBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Reservation.php.html#467"><abbr title="App\Reservation::getReservationShowDataMonthlyAggregatedBy">getReservationShowDataMonthlyAggregatedBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Rules/InvisibleRecaptchaRule.php.html#16"><abbr title="App\Rules\InvisibleRecaptchaRule::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#59"><abbr title="App\Services\Offer\BasicDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#71"><abbr title="App\Services\Offer\BasicDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#105"><abbr title="App\Services\Offer\BasicDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#129"><abbr title="App\Services\Offer\BasicDiscount::getPeriodPrice">getPeriodPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/CouponDiscount.php.html#22"><abbr title="App\Services\Offer\CouponDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/CouponDiscount.php.html#29"><abbr title="App\Services\Offer\CouponDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#60"><abbr title="App\Services\Offer\DateRange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#76"><abbr title="App\Services\Offer\DateRange::setPeriodCounters">setPeriodCounters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#133"><abbr title="App\Services\Offer\DateRange::getPeriodCounters">getPeriodCounters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#143"><abbr title="App\Services\Offer\DateRange::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#158"><abbr title="App\Services\Offer\DateRange::getExtraDayCharge">getExtraDayCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#166"><abbr title="App\Services\Offer\DateRange::getPickupDateTime">getPickupDateTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#176"><abbr title="App\Services\Offer\DateRange::getPeriod">getPeriod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#223"><abbr title="App\Services\Offer\DateRange::getDaysIndex">getDaysIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/PredefinedOffers.php.html#22"><abbr title="App\Services\Offer\PredefinedOffers::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/PredefinedOffers.php.html#32"><abbr title="App\Services\Offer\PredefinedOffers::getPredefinedOffers">getPredefinedOffers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#80"><abbr title="App\Services\Offer\RentalOffer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#106"><abbr title="App\Services\Offer\RentalOffer::getDiscountPercentage">getDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#111"><abbr title="App\Services\Offer\RentalOffer::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#122"><abbr title="App\Services\Offer\RentalOffer::getTotalDiscountPrice">getTotalDiscountPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#140"><abbr title="App\Services\Offer\RentalOffer::getRangePrice">getRangePrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#177"><abbr title="App\Services\Offer\RentalOffer::getBasePrice">getBasePrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#187"><abbr title="App\Services\Offer\RentalOffer::getAccessoriesPrice">getAccessoriesPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#208"><abbr title="App\Services\Offer\RentalOffer::getAccessoryPrice">getAccessoryPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#226"><abbr title="App\Services\Offer\RentalOffer::hasExtraDayCharge">hasExtraDayCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#235"><abbr title="App\Services\Offer\RentalOffer::hasExtraMilesCharge">hasExtraMilesCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#250"><abbr title="App\Services\Offer\RentalOffer::getExtraMilesCharge">getExtraMilesCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#259"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationPickupCharge">hasRemoteLocationPickupCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#277"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationDropoffCharge">hasRemoteLocationDropoffCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#291"><abbr title="App\Services\Offer\RentalOffer::getRemoteLocationCharge">getRemoteLocationCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#332"><abbr title="App\Services\Offer\RentalOffer::hasAfterHoursCharge">hasAfterHoursCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#368"><abbr title="App\Services\Offer\RentalOffer::getAfterHoursCharge">getAfterHoursCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#377"><abbr title="App\Services\Offer\RentalOffer::hasFuelPlanCharge">hasFuelPlanCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#382"><abbr title="App\Services\Offer\RentalOffer::getFuelPlanCharge">getFuelPlanCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#390"><abbr title="App\Services\Offer\RentalOffer::hasAvailability">hasAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#398"><abbr title="App\Services\Offer\RentalOffer::setAvailability">setAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#439"><abbr title="App\Services\Offer\RentalOffer::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#450"><abbr title="App\Services\Offer\RentalOffer::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#460"><abbr title="App\Services\Offer\RentalOffer::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#66"><abbr title="App\Services\Offer\StaticDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#78"><abbr title="App\Services\Offer\StaticDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#109"><abbr title="App\Services\Offer\StaticDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#135"><abbr title="App\Services\Offer\StaticDiscount::calculateTotalPrice">calculateTotalPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ContactLeadSearch.php.html#11"><abbr title="App\Services\Search\ContactLeadSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/CustomerSearch.php.html#11"><abbr title="App\Services\Search\CustomerSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/AuthorShownName.php.html#23"><abbr title="App\Services\Search\Filters\AuthorShownName::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/AuthorTitle.php.html#23"><abbr title="App\Services\Search\Filters\AuthorTitle::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/CreatedAfter.php.html#23"><abbr title="App\Services\Search\Filters\CreatedAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/CreatedBefore.php.html#23"><abbr title="App\Services\Search\Filters\CreatedBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Email.php.html#23"><abbr title="App\Services\Search\Filters\Email::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Enabled.php.html#23"><abbr title="App\Services\Search\Filters\Enabled::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Featured.php.html#23"><abbr title="App\Services\Search\Filters\Featured::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#16"><abbr title="App\Services\Search\Filters\GlobalFilter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#28"><abbr title="App\Services\Search\Filters\GlobalFilter::filterEqual">filterEqual</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#40"><abbr title="App\Services\Search\Filters\GlobalFilter::filterLike">filterLike</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#52"><abbr title="App\Services\Search\Filters\GlobalFilter::filterTranslationLike">filterTranslationLike</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#64"><abbr title="App\Services\Search\Filters\GlobalFilter::filterGTE">filterGTE</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#76"><abbr title="App\Services\Search\Filters\GlobalFilter::filterLTE">filterLTE</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#88"><abbr title="App\Services\Search\Filters\GlobalFilter::filterYN">filterYN</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#108"><abbr title="App\Services\Search\Filters\GlobalFilter::filterRelatedId">filterRelatedId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#122"><abbr title="App\Services\Search\Filters\GlobalFilter::filterBoolean">filterBoolean</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#143"><abbr title="App\Services\Search\Filters\GlobalFilter::filterPseudoBoolean">filterPseudoBoolean</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#163"><abbr title="App\Services\Search\Filters\GlobalFilter::filterTranslatedIn">filterTranslatedIn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/GroupId.php.html#23"><abbr title="App\Services\Search\Filters\GroupId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasCategories.php.html#23"><abbr title="App\Services\Search\Filters\HasCategories::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasLocation.php.html#23"><abbr title="App\Services\Search\Filters\HasLocation::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasMotifs.php.html#23"><abbr title="App\Services\Search\Filters\HasMotifs::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasReservation.php.html#23"><abbr title="App\Services\Search\Filters\HasReservation::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/HasTags.php.html#23"><abbr title="App\Services\Search\Filters\HasTags::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Id.php.html#23"><abbr title="App\Services\Search\Filters\Id::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Intercepted.php.html#23"><abbr title="App\Services\Search\Filters\Intercepted::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Language.php.html#23"><abbr title="App\Services\Search\Filters\Language::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/LocationId.php.html#23"><abbr title="App\Services\Search\Filters\LocationId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Manufacturer.php.html#23"><abbr title="App\Services\Search\Filters\Manufacturer::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Model.php.html#23"><abbr title="App\Services\Search\Filters\Model::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/MotifRelatedId.php.html#23"><abbr title="App\Services\Search\Filters\MotifRelatedId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Name.php.html#23"><abbr title="App\Services\Search\Filters\Name::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/OfferId.php.html#23"><abbr title="App\Services\Search\Filters\OfferId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Origin.php.html#23"><abbr title="App\Services\Search\Filters\Origin::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PickupAfter.php.html#23"><abbr title="App\Services\Search\Filters\PickupAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PickupBefore.php.html#23"><abbr title="App\Services\Search\Filters\PickupBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Published.php.html#23"><abbr title="App\Services\Search\Filters\Published::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PublishedCretanrentals.php.html#23"><abbr title="App\Services\Search\Filters\PublishedCretanrentals::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/PublishedEurodollar.php.html#23"><abbr title="App\Services\Search\Filters\PublishedEurodollar::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReservationId.php.html#23"><abbr title="App\Services\Search\Filters\ReservationId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewId.php.html#23"><abbr title="App\Services\Search\Filters\ReviewId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewRating.php.html#23"><abbr title="App\Services\Search\Filters\ReviewRating::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ReviewText.php.html#23"><abbr title="App\Services\Search\Filters\ReviewText::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Sent.php.html#23"><abbr title="App\Services\Search\Filters\Sent::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Show.php.html#23"><abbr title="App\Services\Search\Filters\Show::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/ShowVerified.php.html#23"><abbr title="App\Services\Search\Filters\ShowVerified::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Site.php.html#23"><abbr title="App\Services\Search\Filters\Site::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Testimonialised.php.html#23"><abbr title="App\Services\Search\Filters\Testimonialised::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Title.php.html#23"><abbr title="App\Services\Search\Filters\Title::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/Translated.php.html#23"><abbr title="App\Services\Search\Filters\Translated::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/TranslationTitle.php.html#23"><abbr title="App\Services\Search\Filters\TranslationTitle::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/UpdatedAfter.php.html#23"><abbr title="App\Services\Search\Filters\UpdatedAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Filters/UpdatedBefore.php.html#23"><abbr title="App\Services\Search\Filters\UpdatedBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/GoogleReviewSearch.php.html#11"><abbr title="App\Services\Search\GoogleReviewSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ListingSearch.php.html#11"><abbr title="App\Services\Search\ListingSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/OfferSearch.php.html#11"><abbr title="App\Services\Search\OfferSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/PostSearch.php.html#11"><abbr title="App\Services\Search\PostSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/ReservationSearch.php.html#11"><abbr title="App\Services\Search\ReservationSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#14"><abbr title="App\Services\Search\Search::applyDecoratorsFromFilters">applyDecoratorsFromFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#28"><abbr title="App\Services\Search\Search::createFilterDecorator">createFilterDecorator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#33"><abbr title="App\Services\Search\Search::isValidDecorator">isValidDecorator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#38"><abbr title="App\Services\Search\Search::getResults">getResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/AccessoryValidator.php.html#38"><abbr title="App\Services\Validation\AccessoryValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/CouponValidator.php.html#33"><abbr title="App\Services\Validation\CouponValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/LocationValidator.php.html#36"><abbr title="App\Services\Validation\LocationValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/ReservationValidator.php.html#22"><abbr title="App\Services\Validation\ReservationValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/ValidationException.php.html#22"><abbr title="App\Services\Validation\ValidationException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/ValidationException.php.html#34"><abbr title="App\Services\Validation\ValidationException::getErrors">getErrors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#16"><abbr title="App\Services\Validation\Validator::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#33"><abbr title="App\Services\Validation\Validator::validateForCreation">validateForCreation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#46"><abbr title="App\Services\Validation\Validator::validateForUpdate">validateForUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Supergroup.php.html#35"><abbr title="App\Supergroup::sluggable">sluggable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag.php.html#23"><abbr title="App\Tag::sluggable">sluggable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/BlogDetailsComposer.php.html#18"><abbr title="App\View\Composers\BlogDetailsComposer::compose">compose</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/FaqPartialComposer.php.html#19"><abbr title="App\View\Composers\FaqPartialComposer::compose">compose</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/SearchBucketComposer.php.html#20"><abbr title="App\View\Composers\SearchBucketComposer::compose">compose</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/Composers/TestimonialPartialComposer.php.html#20"><abbr title="App\View\Composers\TestimonialPartialComposer::compose">compose</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ImageHandling/ImageableTrait.php.html#24"><abbr title="App\Services\ImageHandling\ImageableTrait::deleteImage">deleteImage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/ImageHandling/ImageableTrait.php.html#43"><abbr title="App\Services\ImageHandling\ImageableTrait::storeImage">storeImage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Services/PhotoHandling/PhotoableTrait.php.html#15"><abbr title="App\Services\PhotoHandling\PhotoableTrait::storePhoto">storePhoto</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/CustomerAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\CustomerAccessorsMutators::getCountry">getCountry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/FeedbackAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\FeedbackAccessorsMutators::getAverage">getAverage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#8"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getFullNameAttribute">getFullNameAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getFullNameSEOAttribute">getFullNameSEOAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#24"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getFullNameSEOCRAttribute">getFullNameSEOCRAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getGroupCarsTitle">getGroupCarsTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#46"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getGroupImage">getGroupImage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#57"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getOfferTitleAttribute">getOfferTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#15"><abbr title="App\Traits\AccessorsMutators\ImageAccessorsMutators::getPath">getPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#24"><abbr title="App\Traits\AccessorsMutators\ImageAccessorsMutators::getListingPath">getListingPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\ImageAccessorsMutators::getLocationPath">getLocationPath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#18"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getOffer">getOffer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#30"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getTitleAttribute">getTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#40"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getLongTitleAttribute">getLongTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#59"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getShortTitleAttribute">getShortTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#69"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getSEOTitleAttribute">getSEOTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#79"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getSEOShortTitleAttribute">getSEOShortTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#85"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\LocationAccessorsMutators::isAirport">isAirport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\LocationAccessorsMutators::getGroupedForDropdown">getGroupedForDropdown</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/OfferAccessorsMutators.php.html#12"><abbr title="App\Traits\AccessorsMutators\OfferAccessorsMutators::getCompactDetails">getCompactDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PhotoAccessorsMutators.php.html#11"><abbr title="App\Traits\AccessorsMutators\PhotoAccessorsMutators::getSeoAttributes">getSeoAttributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#17"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#27"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getPickupLocationDescription">getPickupLocationDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#37"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getPickupLocationImages">getPickupLocationImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#47"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#57"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getDropoffLocationDescription">getDropoffLocationDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PickupDropoffLocationAccessorsMutators.php.html#67"><abbr title="App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators::getDropoffLocationImages">getDropoffLocationImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#16"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#27"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#37"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getDates">getDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#51"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::setPickupDateAttribute">setPickupDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#61"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::setDropoffDateAttribute">setDropoffDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#72"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getTitleAttribute">getTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#18"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getListingTitle">getListingTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#28"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#38"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#48"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getDates">getDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#62"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setPickupDateAttribute">setPickupDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#77"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setDropoffDateAttribute">setDropoffDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#92"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setDropoffLocationAttribute">setDropoffLocationAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#104"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::hasCustomer">hasCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#118"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#19"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getGroup">getGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#29"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getDates">getDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#43"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::setPickupDateAttribute">setPickupDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#53"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::setDropoffDateAttribute">setDropoffDateAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#62"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getAccessories">getAccessories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#73"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getCompactDetails">getCompactDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#87"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getTitleAttribute">getTitleAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/CategoryRelationships.php.html#12"><abbr title="App\Traits\Relationships\CategoryRelationships::publishedListings">publishedListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/CustomerRelationships.php.html#11"><abbr title="App\Traits\Relationships\CustomerRelationships::reservations">reservations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/CustomerRelationships.php.html#19"><abbr title="App\Traits\Relationships\CustomerRelationships::quotes">quotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/FeedbackRelationships.php.html#12"><abbr title="App\Traits\Relationships\FeedbackRelationships::reservation">reservation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GoogleReviewRelationships.php.html#14"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::reservation">reservation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GoogleReviewRelationships.php.html#22"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::location">location</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GoogleReviewRelationships.php.html#30"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::language">language</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GroupRelationships.php.html#11"><abbr title="App\Traits\Relationships\GroupRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GroupRelationships.php.html#16"><abbr title="App\Traits\Relationships\GroupRelationships::relatedGroups">relatedGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GroupRelationships.php.html#26"><abbr title="App\Traits\Relationships\GroupRelationships::periods">periods</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/GroupRelationships.php.html#34"><abbr title="App\Traits\Relationships\GroupRelationships::supergroup">supergroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ImageRelationships.php.html#8"><abbr title="App\Traits\Relationships\ImageRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ImageRelationships.php.html#13"><abbr title="App\Traits\Relationships\ImageRelationships::locations">locations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LanguageRelationships.php.html#13"><abbr title="App\Traits\Relationships\LanguageRelationships::googleReviews">googleReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#15"><abbr title="App\Traits\Relationships\ListingRelationships::group">group</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#20"><abbr title="App\Traits\Relationships\ListingRelationships::categories">categories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#25"><abbr title="App\Traits\Relationships\ListingRelationships::getCategoryList">getCategoryList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#30"><abbr title="App\Traits\Relationships\ListingRelationships::images">images</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#43"><abbr title="App\Traits\Relationships\ListingRelationships::related">related</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#60"><abbr title="App\Traits\Relationships\ListingRelationships::suggested">suggested</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#79"><abbr title="App\Traits\Relationships\ListingRelationships::similar">similar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#87"><abbr title="App\Traits\Relationships\ListingRelationships::motifs">motifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#98"><abbr title="App\Traits\Relationships\ListingRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ListingRelationships.php.html#111"><abbr title="App\Traits\Relationships\ListingRelationships::presentablePosts">presentablePosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocalisedMotifRelationships.php.html#14"><abbr title="App\Traits\Relationships\LocalisedMotifRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocalisedMotifRelationships.php.html#22"><abbr title="App\Traits\Relationships\LocalisedMotifRelationships::publishedPosts">publishedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#10"><abbr title="App\Traits\Relationships\LocationRelationships::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#18"><abbr title="App\Traits\Relationships\LocationRelationships::images">images</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#26"><abbr title="App\Traits\Relationships\LocationRelationships::googleReviews">googleReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/MotifRelationships.php.html#16"><abbr title="App\Traits\Relationships\MotifRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/MotifRelationships.php.html#24"><abbr title="App\Traits\Relationships\MotifRelationships::publishedPosts">publishedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/MotifRelationships.php.html#34"><abbr title="App\Traits\Relationships\MotifRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/MotifRelationships.php.html#43"><abbr title="App\Traits\Relationships\MotifRelationships::publishedListings">publishedListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#21"><abbr title="App\Traits\Relationships\OfferRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#32"><abbr title="App\Traits\Relationships\OfferRelationships::customer">customer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#43"><abbr title="App\Traits\Relationships\OfferRelationships::listing">listing</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#52"><abbr title="App\Traits\Relationships\OfferRelationships::pickup_loc">pickup_loc</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#61"><abbr title="App\Traits\Relationships\OfferRelationships::dropoff_loc">dropoff_loc</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#70"><abbr title="App\Traits\Relationships\OfferRelationships::accessories">accessories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/OfferRelationships.php.html#79"><abbr title="App\Traits\Relationships\OfferRelationships::reservations">reservations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PeriodRelationships.php.html#16"><abbr title="App\Traits\Relationships\PeriodRelationships::groups">groups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PeriodRelationships.php.html#24"><abbr title="App\Traits\Relationships\PeriodRelationships::changes">changes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PeriodRelationships.php.html#32"><abbr title="App\Traits\Relationships\PeriodRelationships::changesGroup">changesGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PeriodRelationships.php.html#41"><abbr title="App\Traits\Relationships\PeriodRelationships::changesGroupYear">changesGroupYear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#18"><abbr title="App\Traits\Relationships\PostRelationships::tags">tags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#28"><abbr title="App\Traits\Relationships\PostRelationships::motifs">motifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#37"><abbr title="App\Traits\Relationships\PostRelationships::localisedMotifs">localisedMotifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#43"><abbr title="App\Traits\Relationships\PostRelationships::getMotifedPosts">getMotifedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#72"><abbr title="App\Traits\Relationships\PostRelationships::getRelatedPosts">getRelatedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostTranslationRelationships.php.html#13"><abbr title="App\Traits\Relationships\PostTranslationRelationships::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/PostTranslationRelationships.php.html#21"><abbr title="App\Traits\Relationships\PostTranslationRelationships::publishedPost">publishedPost</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/QuoteRelationships.php.html#12"><abbr title="App\Traits\Relationships\QuoteRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ReservationRelationships.php.html#16"><abbr title="App\Traits\Relationships\ReservationRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ReservationRelationships.php.html#25"><abbr title="App\Traits\Relationships\ReservationRelationships::feedback">feedback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ReservationRelationships.php.html#34"><abbr title="App\Traits\Relationships\ReservationRelationships::googleReview">googleReview</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/ReservationRelationships.php.html#43"><abbr title="App\Traits\Relationships\ReservationRelationships::offer">offer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/SupergroupRelationships.php.html#15"><abbr title="App\Traits\Relationships\SupergroupRelationships::groups">groups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/SupergroupRelationships.php.html#23"><abbr title="App\Traits\Relationships\SupergroupRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Relationships/TagRelationships.php.html#13"><abbr title="App\Traits\Relationships\TagRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/GoogleReviewScopes.php.html#13"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopePublished">scopePublished</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/GoogleReviewScopes.php.html#24"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeTestimonialised">scopeTestimonialised</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/GoogleReviewScopes.php.html#35"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeShowable">scopeShowable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/GoogleReviewScopes.php.html#53"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeTestimoniable">scopeTestimoniable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/ListingScopes.php.html#13"><abbr title="App\Traits\Scopes\ListingScopes::scopePublishedEurodollar">scopePublishedEurodollar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/ListingScopes.php.html#24"><abbr title="App\Traits\Scopes\ListingScopes::scopePublishedCretanrentals">scopePublishedCretanrentals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#14"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeEl">scopeEl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#25"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeEn">scopeEn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#36"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeDe">scopeDe</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#47"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeFr">scopeFr</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/LocalisableScopes.php.html#58"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeIt">scopeIt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/PostScopes.php.html#13"><abbr title="App\Traits\Scopes\PostScopes::scopePublished">scopePublished</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/PostScopes.php.html#24"><abbr title="App\Traits\Scopes\PostScopes::scopeFeatured">scopeFeatured</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Traits/Scopes/ReservationScopes.php.html#13"><abbr title="App\Traits\Scopes\ReservationScopes::scopeOnlyShow">scopeOnlyShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Providers/AuthServiceProvider.php.html#22"><abbr title="App\Providers\AuthServiceProvider::boot">boot</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Providers/AppServiceProvider.php.html#22"><abbr title="App\Providers\AppServiceProvider::boot">boot</abbr></a></td><td class="text-right">70%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Reservation.php.html#89"><abbr title="App\Reservation::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#27"><abbr title="App\Http\Controllers\ReservationsController::index">index</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#255"><abbr title="App\Http\Controllers\ListingsController::show">show</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#172"><abbr title="App\Http\Controllers\BookingsController::initialSetupOfCookie">initialSetupOfCookie</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Offer.php.html#44"><abbr title="App\Offer::isReady">isReady</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#477"><abbr title="App\Http\Controllers\ListingsController::getPrice">getPrice</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Http/Controllers/ListingsController.php.html#36"><abbr title="App\Http\Controllers\ListingsController::index">index</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Quote.php.html#31"><abbr title="App\Quote::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Services/Offer/PredefinedOffers.php.html#32"><abbr title="App\Services\Offer\PredefinedOffers::getPredefinedOffers">getPredefinedOffers</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#140"><abbr title="App\Services\Offer\RentalOffer::getRangePrice">getRangePrice</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Traits/Relationships/PostRelationships.php.html#72"><abbr title="App\Traits\Relationships\PostRelationships::getRelatedPosts">getRelatedPosts</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Http/Controllers/BookingsController.php.html#80"><abbr title="App\Http\Controllers\BookingsController::fleetIndex">fleetIndex</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#165"><abbr title="App\Http\Controllers\Controller::popup">popup</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#59"><abbr title="App\Http\Controllers\FeedbackController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Http/Requests/ReservationRequest.php.html#31"><abbr title="App\Http\Requests\ReservationRequest::sanitize">sanitize</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#76"><abbr title="App\Services\Offer\DateRange::setPeriodCounters">setPeriodCounters</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#291"><abbr title="App\Services\Offer\RentalOffer::getRemoteLocationCharge">getRemoteLocationCharge</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/PhotoAccessorsMutators.php.html#11"><abbr title="App\Traits\AccessorsMutators\PhotoAccessorsMutators::getSeoAttributes">getSeoAttributes</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#32"><abbr title="App\Http\Controllers\Controller::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#48"><abbr title="App\Http\Controllers\PostsController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Jobs/SendReservationFeedback.php.html#42"><abbr title="App\Jobs\SendReservationFeedback::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Location.php.html#61"><abbr title="App\Location::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#176"><abbr title="App\Services\Offer\DateRange::getPeriod">getPeriod</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#398"><abbr title="App\Services\Offer\RentalOffer::setAvailability">setAvailability</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#86"><abbr title="App\Http\Controllers\CategoriesController::update">update</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#73"><abbr title="App\Http\Controllers\ContactController::handleContact">handleContact</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/FeedbackController.php.html#20"><abbr title="App\Http\Controllers\FeedbackController::create">create</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#18"><abbr title="App\Http\Controllers\ImageController::__construct">__construct</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Location.php.html#100"><abbr title="App\Location::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#146"><abbr title="App\Repositories\ImageRepository::delete">delete</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#176"><abbr title="App\Repositories\ListingRepository::getListings">getListings</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Reservation.php.html#427"><abbr title="App\Reservation::getReservationShowDataAggregatedBy">getReservationShowDataAggregatedBy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Reservation.php.html#467"><abbr title="App\Reservation::getReservationShowDataMonthlyAggregatedBy">getReservationShowDataMonthlyAggregatedBy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#129"><abbr title="App\Services\Offer\BasicDiscount::getPeriodPrice">getPeriodPrice</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#135"><abbr title="App\Services\Offer\StaticDiscount::calculateTotalPrice">calculateTotalPrice</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="View/Composers/BlogDetailsComposer.php.html#18"><abbr title="App\View\Composers\BlogDetailsComposer::compose">compose</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Http/Controllers/FeedController.php.html#25"><abbr title="App\Http\Controllers\FeedController::exportCarsCsv">exportCarsCsv</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#55"><abbr title="App\Http\Controllers\ImageController::getListingImages">getListingImages</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#86"><abbr title="App\Http\Controllers\ImageController::getLocationImages">getLocationImages</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#74"><abbr title="App\Http\Controllers\PlacesController::heraklion">heraklion</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#411"><abbr title="App\Http\Controllers\PlacesController::chania">chania</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#522"><abbr title="App\Http\Controllers\PlacesController::rethymno">rethymno</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#634"><abbr title="App\Http\Controllers\PlacesController::agios_nikolaos">agios_nikolaos</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#130"><abbr title="App\Http\Controllers\PopupController::return_bytes">return_bytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#73"><abbr title="App\Http\Controllers\PricingController::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#113"><abbr title="App\Http\Controllers\PricingController::availabilityUpdate">availabilityUpdate</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#343"><abbr title="App\Http\Controllers\ReservationsController::verify">verify</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Http/Requests/ContactRequest.php.html#31"><abbr title="App\Http\Requests\ContactRequest::sanitize">sanitize</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#52"><abbr title="App\Repositories\ImageRepository::upload">upload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#188"><abbr title="App\Repositories\ImageRepository::sanitize">sanitize</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#102"><abbr title="App\Repositories\ListingRepository::findByTransmission">findByTransmission</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#125"><abbr title="App\Repositories\ListingRepository::findByFuel">findByFuel</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#148"><abbr title="App\Repositories\ListingRepository::findBySeats">findBySeats</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#71"><abbr title="App\Services\Offer\BasicDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#235"><abbr title="App\Services\Offer\RentalOffer::hasExtraMilesCharge">hasExtraMilesCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#259"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationPickupCharge">hasRemoteLocationPickupCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#277"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationDropoffCharge">hasRemoteLocationDropoffCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#332"><abbr title="App\Services\Offer\RentalOffer::hasAfterHoursCharge">hasAfterHoursCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="View/Composers/SearchBucketComposer.php.html#20"><abbr title="App\View\Composers\SearchBucketComposer::compose">compose</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Helpers/ChangePortal.php.html#11"><abbr title="App\Helpers\ChangePortal::LoadPortal">LoadPortal</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Helpers/TimeRange.php.html#14"><abbr title="App\Helpers\TimeRange::create_time_range">create_time_range</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#43"><abbr title="App\Http\Controllers\AccessoriesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/AccessoriesController.php.html#87"><abbr title="App\Http\Controllers\AccessoriesController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/Auth/LoginController.php.html#48"><abbr title="App\Http\Controllers\Auth\LoginController::authenticated">authenticated</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#30"><abbr title="App\Http\Controllers\AuthController::handleLogin">handleLogin</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#46"><abbr title="App\Http\Controllers\CategoriesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#59"><abbr title="App\Http\Controllers\CouponsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#118"><abbr title="App\Http\Controllers\CouponsController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#47"><abbr title="App\Http\Controllers\LocationsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/LocationsController.php.html#93"><abbr title="App\Http\Controllers\LocationsController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/OffersController.php.html#23"><abbr title="App\Http\Controllers\OffersController::show">show</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#184"><abbr title="App\Http\Controllers\PlacesController::heraklion_airport">heraklion_airport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#298"><abbr title="App\Http\Controllers\PlacesController::chania_airport">chania_airport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#57"><abbr title="App\Http\Controllers\QuotesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#55"><abbr title="App\Http\Controllers\RepeatingClientsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#171"><abbr title="App\Http\Controllers\ReservationsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Image.php.html#44"><abbr title="App\Image::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Image.php.html#62"><abbr title="App\Image::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Jobs/SendReservationReminder.php.html#44"><abbr title="App\Jobs\SendReservationReminder::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Livewire/Frontend/ReviewsPage.php.html#24"><abbr title="App\Livewire\Frontend\ReviewsPage::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Reservation.php.html#389"><abbr title="App\Reservation::shouldSendReminder">shouldSendReminder</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Offer/BasicDiscount.php.html#105"><abbr title="App\Services\Offer\BasicDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Offer/CouponDiscount.php.html#29"><abbr title="App\Services\Offer\CouponDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#78"><abbr title="App\Services\Offer\StaticDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Offer/StaticDiscount.php.html#109"><abbr title="App\Services\Offer\StaticDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#88"><abbr title="App\Services\Search\Filters\GlobalFilter::filterYN">filterYN</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#122"><abbr title="App\Services\Search\Filters\GlobalFilter::filterBoolean">filterBoolean</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Search/Filters/GlobalFilter.php.html#143"><abbr title="App\Services\Search\Filters\GlobalFilter::filterPseudoBoolean">filterPseudoBoolean</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/Search/Search.php.html#14"><abbr title="App\Services\Search\Search::applyDecoratorsFromFilters">applyDecoratorsFromFilters</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Services/ImageHandling/ImageableTrait.php.html#24"><abbr title="App\Services\ImageHandling\ImageableTrait::deleteImage">deleteImage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/CustomerAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\CustomerAccessorsMutators::getCountry">getCountry</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#46"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getGroupImage">getGroupImage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\LocationAccessorsMutators::getGroupedForDropdown">getGroupedForDropdown</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Accessory.php.html#28"><abbr title="App\Accessory::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Accessory.php.html#45"><abbr title="App\Accessory::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseModel.php.html#47"><abbr title="App\BaseModel::performCreationValidation">performCreationValidation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BaseModel.php.html#65"><abbr title="App\BaseModel::performUpdateValidation">performUpdateValidation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Category.php.html#33"><abbr title="App\Category::performCustomPostCreationTasks">performCustomPostCreationTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Category.php.html#47"><abbr title="App\Category::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/Auth/TwoFactorController.php.html#26"><abbr title="App\Http\Controllers\Auth\TwoFactorController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/AuthController.php.html#15"><abbr title="App\Http\Controllers\AuthController::showLogin">showLogin</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/CategoriesController.php.html#116"><abbr title="App\Http\Controllers\CategoriesController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/ContactController.php.html#151"><abbr title="App\Http\Controllers\ContactController::handleNewsletter">handleNewsletter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#132"><abbr title="App\Http\Controllers\Controller::setupLayout">setupLayout</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/Controller.php.html#149"><abbr title="App\Http\Controllers\Controller::overwriteOpengraph">overwriteOpengraph</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/CouponsController.php.html#21"><abbr title="App\Http\Controllers\CouponsController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/CustomersController.php.html#14"><abbr title="App\Http\Controllers\CustomersController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/HomeController.php.html#20"><abbr title="App\Http\Controllers\HomeController::getIndex">getIndex</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/ImageController.php.html#40"><abbr title="App\Http\Controllers\ImageController::deleteUpload">deleteUpload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PagesController.php.html#127"><abbr title="App\Http\Controllers\PagesController::faq">faq</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PlacesController.php.html#749"><abbr title="App\Http\Controllers\PlacesController::setDefaultLocation">setDefaultLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#48"><abbr title="App\Http\Controllers\PopupController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PopupController.php.html#90"><abbr title="App\Http\Controllers\PopupController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PostsController.php.html#139"><abbr title="App\Http\Controllers\PostsController::handleIndex">handleIndex</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/PricingController.php.html#37"><abbr title="App\Http\Controllers\PricingController::edit">edit</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#26"><abbr title="App\Http\Controllers\QuotesController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/QuotesController.php.html#132"><abbr title="App\Http\Controllers\QuotesController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#22"><abbr title="App\Http\Controllers\RepeatingClientsController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/RepeatingClientsController.php.html#126"><abbr title="App\Http\Controllers\RepeatingClientsController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#278"><abbr title="App\Http\Controllers\ReservationsController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/ReservationsController.php.html#319"><abbr title="App\Http\Controllers\ReservationsController::confirm">confirm</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Http/Controllers/SearchController.php.html#21"><abbr title="App\Http\Controllers\SearchController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Jobs/SendOfferNotification.php.html#40"><abbr title="App\Jobs\SendOfferNotification::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Jobs/SendReservationVerifyReminder.php.html#42"><abbr title="App\Jobs\SendReservationVerifyReminder::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Location.php.html#43"><abbr title="App\Location::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Popup.php.html#26"><abbr title="App\Popup::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Popup.php.html#42"><abbr title="App\Popup::performCustomUpdateTasks">performCustomUpdateTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RepeatingClient.php.html#25"><abbr title="App\RepeatingClient::performCustomCreationTasks">performCustomCreationTasks</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/Eloquent/Repository.php.html#107"><abbr title="App\Repositories\Eloquent\Repository::orderBy">orderBy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#31"><abbr title="App\Repositories\ImageRepository::setConfigFullSize">setConfigFullSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#44"><abbr title="App\Repositories\ImageRepository::setConfigIconSize">setConfigIconSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/ImageRepository.php.html#103"><abbr title="App\Repositories\ImageRepository::createUniqueFilename">createUniqueFilename</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#34"><abbr title="App\Repositories\ListingRepository::findListings">findListings</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Repositories/ListingRepository.php.html#86"><abbr title="App\Repositories\ListingRepository::findByGroups">findByGroups</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Rules/InvisibleRecaptchaRule.php.html#16"><abbr title="App\Rules\InvisibleRecaptchaRule::validate">validate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#143"><abbr title="App\Services\Offer\DateRange::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/DateRange.php.html#158"><abbr title="App\Services\Offer\DateRange::getExtraDayCharge">getExtraDayCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#122"><abbr title="App\Services\Offer\RentalOffer::getTotalDiscountPrice">getTotalDiscountPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#177"><abbr title="App\Services\Offer\RentalOffer::getBasePrice">getBasePrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#187"><abbr title="App\Services\Offer\RentalOffer::getAccessoriesPrice">getAccessoriesPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#208"><abbr title="App\Services\Offer\RentalOffer::getAccessoryPrice">getAccessoryPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#250"><abbr title="App\Services\Offer\RentalOffer::getExtraMilesCharge">getExtraMilesCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#368"><abbr title="App\Services\Offer\RentalOffer::getAfterHoursCharge">getAfterHoursCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Offer/RentalOffer.php.html#382"><abbr title="App\Services\Offer\RentalOffer::getFuelPlanCharge">getFuelPlanCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Validation/AccessoryValidator.php.html#38"><abbr title="App\Services\Validation\AccessoryValidator::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Validation/LocationValidator.php.html#36"><abbr title="App\Services\Validation\LocationValidator::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Services/Validation/Validator.php.html#16"><abbr title="App\Services\Validation\Validator::validate">validate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="View/Composers/FaqPartialComposer.php.html#19"><abbr title="App\View\Composers\FaqPartialComposer::compose">compose</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getGroupCarsTitle">getGroupCarsTitle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/GroupAccessorsMutators.php.html#57"><abbr title="App\Traits\AccessorsMutators\GroupAccessorsMutators::getOfferTitleAttribute">getOfferTitleAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#24"><abbr title="App\Traits\AccessorsMutators\ImageAccessorsMutators::getListingPath">getListingPath</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ImageAccessorsMutators.php.html#33"><abbr title="App\Traits\AccessorsMutators\ImageAccessorsMutators::getLocationPath">getLocationPath</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ListingAccessorsMutators.php.html#85"><abbr title="App\Traits\AccessorsMutators\ListingAccessorsMutators::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/LocationAccessorsMutators.php.html#13"><abbr title="App\Traits\AccessorsMutators\LocationAccessorsMutators::isAirport">isAirport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#16"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#27"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/QuoteAccessorsMutators.php.html#72"><abbr title="App\Traits\AccessorsMutators\QuoteAccessorsMutators::getTitleAttribute">getTitleAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#18"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getListingTitle">getListingTitle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#28"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#38"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#62"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setPickupDateAttribute">setPickupDateAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#77"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setDropoffDateAttribute">setDropoffDateAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#92"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::setDropoffLocationAttribute">setDropoffLocationAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#104"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::hasCustomer">hasCustomer</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/RepeatingClientAccessorsMutators.php.html#118"><abbr title="App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators::getCustomer">getCustomer</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/AccessorsMutators/ReservationAccessorsMutators.php.html#87"><abbr title="App\Traits\AccessorsMutators\ReservationAccessorsMutators::getTitleAttribute">getTitleAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Traits/Relationships/LocationRelationships.php.html#10"><abbr title="App\Traits\Relationships\LocationRelationships::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 12:51:21 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([177,0,0,0,0,0,0,1,1,0,0,41], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([512,0,0,0,0,0,0,1,1,0,0,14], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"Accessory.php.html#8\">App\\Accessory<\/a>"],[100,0,"<a href=\"AccessoryTranslation.php.html#5\">App\\AccessoryTranslation<\/a>"],[0,8,"<a href=\"BaseModel.php.html#6\">App\\BaseModel<\/a>"],[0,6,"<a href=\"Category.php.html#7\">App\\Category<\/a>"],[100,0,"<a href=\"ContactLead.php.html#5\">App\\ContactLead<\/a>"],[100,0,"<a href=\"Country.php.html#7\">App\\Country<\/a>"],[100,0,"<a href=\"CountryTranslation.php.html#5\">App\\CountryTranslation<\/a>"],[0,7,"<a href=\"Coupon.php.html#6\">App\\Coupon<\/a>"],[100,0,"<a href=\"CouponTranslation.php.html#5\">App\\CouponTranslation<\/a>"],[0,3,"<a href=\"Customer.php.html#8\">App\\Customer<\/a>"],[100,0,"<a href=\"Events\/Event.php.html#5\">App\\Events\\Event<\/a>"],[0,2,"<a href=\"Feedback.php.html#9\">App\\Feedback<\/a>"],[100,0,"<a href=\"GoogleReview.php.html#10\">App\\GoogleReview<\/a>"],[100,0,"<a href=\"GoogleReviewTranslation.php.html#5\">App\\GoogleReviewTranslation<\/a>"],[100,0,"<a href=\"Group.php.html#9\">App\\Group<\/a>"],[100,0,"<a href=\"GroupTranslation.php.html#5\">App\\GroupTranslation<\/a>"],[0,3,"<a href=\"Helpers\/ChangePortal.php.html#9\">App\\Helpers\\ChangePortal<\/a>"],[100,9,"<a href=\"Helpers\/TestHelper.php.html#5\">App\\Helpers\\TestHelper<\/a>"],[0,3,"<a href=\"Helpers\/TimeRange.php.html#4\">App\\Helpers\\TimeRange<\/a>"],[0,10,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#10\">App\\Http\\Controllers\\AccessoriesController<\/a>"],[0,5,"<a href=\"Http\/Controllers\/Auth\/LoginController.php.html#11\">App\\Http\\Controllers\\Auth\\LoginController<\/a>"],[0,5,"<a href=\"Http\/Controllers\/Auth\/TwoFactorController.php.html#9\">App\\Http\\Controllers\\Auth\\TwoFactorController<\/a>"],[0,5,"<a href=\"Http\/Controllers\/AuthController.php.html#10\">App\\Http\\Controllers\\AuthController<\/a>"],[0,24,"<a href=\"Http\/Controllers\/BookingsController.php.html#10\">App\\Http\\Controllers\\BookingsController<\/a>"],[0,13,"<a href=\"Http\/Controllers\/CategoriesController.php.html#10\">App\\Http\\Controllers\\CategoriesController<\/a>"],[0,9,"<a href=\"Http\/Controllers\/ContactController.php.html#20\">App\\Http\\Controllers\\ContactController<\/a>"],[0,18,"<a href=\"Http\/Controllers\/Controller.php.html#24\">App\\Http\\Controllers\\Controller<\/a>"],[0,12,"<a href=\"Http\/Controllers\/CouponsController.php.html#12\">App\\Http\\Controllers\\CouponsController<\/a>"],[0,2,"<a href=\"Http\/Controllers\/CustomersController.php.html#8\">App\\Http\\Controllers\\CustomersController<\/a>"],[0,1,"<a href=\"Http\/Controllers\/DashboardController.php.html#9\">App\\Http\\Controllers\\DashboardController<\/a>"],[0,5,"<a href=\"Http\/Controllers\/FeedController.php.html#8\">App\\Http\\Controllers\\FeedController<\/a>"],[0,15,"<a href=\"Http\/Controllers\/FeedbackController.php.html#13\">App\\Http\\Controllers\\FeedbackController<\/a>"],[0,3,"<a href=\"Http\/Controllers\/HomeController.php.html#9\">App\\Http\\Controllers\\HomeController<\/a>"],[0,16,"<a href=\"Http\/Controllers\/ImageController.php.html#12\">App\\Http\\Controllers\\ImageController<\/a>"],[0,39,"<a href=\"Http\/Controllers\/ListingsController.php.html#20\">App\\Http\\Controllers\\ListingsController<\/a>"],[0,10,"<a href=\"Http\/Controllers\/LocationsController.php.html#10\">App\\Http\\Controllers\\LocationsController<\/a>"],[0,3,"<a href=\"Http\/Controllers\/OffersController.php.html#14\">App\\Http\\Controllers\\OffersController<\/a>"],[0,12,"<a href=\"Http\/Controllers\/PagesController.php.html#8\">App\\Http\\Controllers\\PagesController<\/a>"],[0,25,"<a href=\"Http\/Controllers\/PlacesController.php.html#12\">App\\Http\\Controllers\\PlacesController<\/a>"],[0,12,"<a href=\"Http\/Controllers\/PopupController.php.html#11\">App\\Http\\Controllers\\PopupController<\/a>"],[0,11,"<a href=\"Http\/Controllers\/PostsController.php.html#14\">App\\Http\\Controllers\\PostsController<\/a>"],[0,11,"<a href=\"Http\/Controllers\/PricingController.php.html#13\">App\\Http\\Controllers\\PricingController<\/a>"],[0,10,"<a href=\"Http\/Controllers\/QuotesController.php.html#18\">App\\Http\\Controllers\\QuotesController<\/a>"],[0,11,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#15\">App\\Http\\Controllers\\RepeatingClientsController<\/a>"],[0,31,"<a href=\"Http\/Controllers\/ReservationsController.php.html#20\">App\\Http\\Controllers\\ReservationsController<\/a>"],[0,2,"<a href=\"Http\/Controllers\/ReviewsController.php.html#5\">App\\Http\\Controllers\\ReviewsController<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RobotsController.php.html#4\">App\\Http\\Controllers\\RobotsController<\/a>"],[0,3,"<a href=\"Http\/Controllers\/SearchController.php.html#10\">App\\Http\\Controllers\\SearchController<\/a>"],[0,2,"<a href=\"Http\/Controllers\/SessionsController.php.html#7\">App\\Http\\Controllers\\SessionsController<\/a>"],[0,3,"<a href=\"Http\/Controllers\/TestController.php.html#9\">App\\Http\\Controllers\\TestController<\/a>"],[0,2,"<a href=\"Http\/Requests\/AvailabilityRequest.php.html#7\">App\\Http\\Requests\\AvailabilityRequest<\/a>"],[0,6,"<a href=\"Http\/Requests\/ContactRequest.php.html#7\">App\\Http\\Requests\\ContactRequest<\/a>"],[0,2,"<a href=\"Http\/Requests\/PricingRequest.php.html#7\">App\\Http\\Requests\\PricingRequest<\/a>"],[100,0,"<a href=\"Http\/Requests\/Request.php.html#7\">App\\Http\\Requests\\Request<\/a>"],[0,10,"<a href=\"Http\/Requests\/ReservationRequest.php.html#7\">App\\Http\\Requests\\ReservationRequest<\/a>"],[0,7,"<a href=\"Image.php.html#8\">App\\Image<\/a>"],[100,0,"<a href=\"Jobs\/Command.php.html#3\">App\\Jobs\\Command<\/a>"],[100,0,"<a href=\"Jobs\/Job.php.html#7\">App\\Jobs\\Job<\/a>"],[0,1,"<a href=\"Jobs\/SendDuplicateReservationReminder.php.html#14\">App\\Jobs\\SendDuplicateReservationReminder<\/a>"],[0,3,"<a href=\"Jobs\/SendOfferNotification.php.html#16\">App\\Jobs\\SendOfferNotification<\/a>"],[0,7,"<a href=\"Jobs\/SendReservationFeedback.php.html#21\">App\\Jobs\\SendReservationFeedback<\/a>"],[0,4,"<a href=\"Jobs\/SendReservationReminder.php.html#23\">App\\Jobs\\SendReservationReminder<\/a>"],[0,3,"<a href=\"Jobs\/SendReservationVerifyReminder.php.html#21\">App\\Jobs\\SendReservationVerifyReminder<\/a>"],[100,0,"<a href=\"Language.php.html#8\">App\\Language<\/a>"],[100,0,"<a href=\"LanguageTranslation.php.html#5\">App\\LanguageTranslation<\/a>"],[0,3,"<a href=\"Listing.php.html#22\">App\\Listing<\/a>"],[100,0,"<a href=\"ListingTranslation.php.html#5\">App\\ListingTranslation<\/a>"],[0,4,"<a href=\"Livewire\/Frontend\/ReviewsPage.php.html#10\">App\\Livewire\\Frontend\\ReviewsPage<\/a>"],[100,0,"<a href=\"LocalisedMotif.php.html#9\">App\\LocalisedMotif<\/a>"],[0,15,"<a href=\"Location.php.html#11\">App\\Location<\/a>"],[100,0,"<a href=\"LocationTranslation.php.html#5\">App\\LocationTranslation<\/a>"],[100,0,"<a href=\"LogPricing.php.html#7\">App\\LogPricing<\/a>"],[0,2,"<a href=\"Models\/User.php.html#11\">App\\Models\\User<\/a>"],[100,0,"<a href=\"Motif.php.html#8\">App\\Motif<\/a>"],[100,0,"<a href=\"Newsletter.php.html#5\">App\\Newsletter<\/a>"],[0,4,"<a href=\"Notifications\/TwoFactorCode.php.html#10\">App\\Notifications\\TwoFactorCode<\/a>"],[0,13,"<a href=\"Offer.php.html#8\">App\\Offer<\/a>"],[100,0,"<a href=\"Period.php.html#8\">App\\Period<\/a>"],[100,0,"<a href=\"Photo.php.html#11\">App\\Photo<\/a>"],[100,0,"<a href=\"PhotoTranslation.php.html#7\">App\\PhotoTranslation<\/a>"],[0,5,"<a href=\"Popup.php.html#8\">App\\Popup<\/a>"],[0,3,"<a href=\"Post.php.html#23\">App\\Post<\/a>"],[0,2,"<a href=\"PostTranslation.php.html#12\">App\\PostTranslation<\/a>"],[0,1,"<a href=\"Pricing.php.html#9\">App\\Pricing<\/a>"],[72.72727272727273,2,"<a href=\"Providers\/AppServiceProvider.php.html#9\">App\\Providers\\AppServiceProvider<\/a>"],[66.66666666666666,1,"<a href=\"Providers\/AuthServiceProvider.php.html#8\">App\\Providers\\AuthServiceProvider<\/a>"],[0,1,"<a href=\"Providers\/BroadcastServiceProvider.php.html#8\">App\\Providers\\BroadcastServiceProvider<\/a>"],[0,2,"<a href=\"Providers\/BusServiceProvider.php.html#6\">App\\Providers\\BusServiceProvider<\/a>"],[0,1,"<a href=\"Providers\/ConfigServiceProvider.php.html#5\">App\\Providers\\ConfigServiceProvider<\/a>"],[100,2,"<a href=\"Providers\/EventServiceProvider.php.html#10\">App\\Providers\\EventServiceProvider<\/a>"],[100,2,"<a href=\"Providers\/RepositoryServiceProvider.php.html#5\">App\\Providers\\RepositoryServiceProvider<\/a>"],[100,2,"<a href=\"Providers\/ViewServiceProvider.php.html#12\">App\\Providers\\ViewServiceProvider<\/a>"],[0,10,"<a href=\"Quote.php.html#12\">App\\Quote<\/a>"],[0,3,"<a href=\"RepeatingClient.php.html#5\">App\\RepeatingClient<\/a>"],[0,13,"<a href=\"Repositories\/Eloquent\/Repository.php.html#6\">App\\Repositories\\Eloquent\\Repository<\/a>"],[0,24,"<a href=\"Repositories\/ImageRepository.php.html#15\">App\\Repositories\\ImageRepository<\/a>"],[0,25,"<a href=\"Repositories\/ListingRepository.php.html#11\">App\\Repositories\\ListingRepository<\/a>"],[0,1,"<a href=\"Repositories\/PeriodRepository.php.html#10\">App\\Repositories\\PeriodRepository<\/a>"],[0,41,"<a href=\"Reservation.php.html#19\">App\\Reservation<\/a>"],[100,0,"<a href=\"ReservationToAccessory.php.html#5\">App\\ReservationToAccessory<\/a>"],[0,2,"<a href=\"Rules\/InvisibleRecaptchaRule.php.html#9\">App\\Rules\\InvisibleRecaptchaRule<\/a>"],[100,0,"<a href=\"SearchQuery.php.html#7\">App\\SearchQuery<\/a>"],[0,13,"<a href=\"Services\/Offer\/BasicDiscount.php.html#16\">App\\Services\\Offer\\BasicDiscount<\/a>"],[0,4,"<a href=\"Services\/Offer\/CouponDiscount.php.html#14\">App\\Services\\Offer\\CouponDiscount<\/a>"],[0,21,"<a href=\"Services\/Offer\/DateRange.php.html#16\">App\\Services\\Offer\\DateRange<\/a>"],[100,0,"<a href=\"Services\/Offer\/DiscountBehaviour.php.html#12\">App\\Services\\Offer\\DiscountBehaviour<\/a>"],[0,10,"<a href=\"Services\/Offer\/PredefinedOffers.php.html#18\">App\\Services\\Offer\\PredefinedOffers<\/a>"],[0,61,"<a href=\"Services\/Offer\/RentalOffer.php.html#20\">App\\Services\\Offer\\RentalOffer<\/a>"],[0,12,"<a href=\"Services\/Offer\/StaticDiscount.php.html#16\">App\\Services\\Offer\\StaticDiscount<\/a>"],[0,1,"<a href=\"Services\/Search\/ContactLeadSearch.php.html#8\">App\\Services\\Search\\ContactLeadSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/CustomerSearch.php.html#8\">App\\Services\\Search\\CustomerSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/AuthorShownName.php.html#7\">App\\Services\\Search\\Filters\\AuthorShownName<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/AuthorTitle.php.html#7\">App\\Services\\Search\\Filters\\AuthorTitle<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/CreatedAfter.php.html#7\">App\\Services\\Search\\Filters\\CreatedAfter<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/CreatedBefore.php.html#7\">App\\Services\\Search\\Filters\\CreatedBefore<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Email.php.html#7\">App\\Services\\Search\\Filters\\Email<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Enabled.php.html#7\">App\\Services\\Search\\Filters\\Enabled<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Featured.php.html#7\">App\\Services\\Search\\Filters\\Featured<\/a>"],[0,17,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#7\">App\\Services\\Search\\Filters\\GlobalFilter<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GroupId.php.html#7\">App\\Services\\Search\\Filters\\GroupId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasCategories.php.html#7\">App\\Services\\Search\\Filters\\HasCategories<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasLocation.php.html#7\">App\\Services\\Search\\Filters\\HasLocation<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasMotifs.php.html#7\">App\\Services\\Search\\Filters\\HasMotifs<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasReservation.php.html#7\">App\\Services\\Search\\Filters\\HasReservation<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasTags.php.html#7\">App\\Services\\Search\\Filters\\HasTags<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Id.php.html#7\">App\\Services\\Search\\Filters\\Id<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Intercepted.php.html#7\">App\\Services\\Search\\Filters\\Intercepted<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Language.php.html#7\">App\\Services\\Search\\Filters\\Language<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/LocationId.php.html#7\">App\\Services\\Search\\Filters\\LocationId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Manufacturer.php.html#7\">App\\Services\\Search\\Filters\\Manufacturer<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Model.php.html#7\">App\\Services\\Search\\Filters\\Model<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/MotifRelatedId.php.html#7\">App\\Services\\Search\\Filters\\MotifRelatedId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Name.php.html#7\">App\\Services\\Search\\Filters\\Name<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/OfferId.php.html#7\">App\\Services\\Search\\Filters\\OfferId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Origin.php.html#7\">App\\Services\\Search\\Filters\\Origin<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PickupAfter.php.html#7\">App\\Services\\Search\\Filters\\PickupAfter<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PickupBefore.php.html#7\">App\\Services\\Search\\Filters\\PickupBefore<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Published.php.html#7\">App\\Services\\Search\\Filters\\Published<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PublishedCretanrentals.php.html#7\">App\\Services\\Search\\Filters\\PublishedCretanrentals<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PublishedEurodollar.php.html#7\">App\\Services\\Search\\Filters\\PublishedEurodollar<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReservationId.php.html#7\">App\\Services\\Search\\Filters\\ReservationId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewId.php.html#7\">App\\Services\\Search\\Filters\\ReviewId<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewRating.php.html#7\">App\\Services\\Search\\Filters\\ReviewRating<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewText.php.html#7\">App\\Services\\Search\\Filters\\ReviewText<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Sent.php.html#7\">App\\Services\\Search\\Filters\\Sent<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Show.php.html#7\">App\\Services\\Search\\Filters\\Show<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ShowVerified.php.html#7\">App\\Services\\Search\\Filters\\ShowVerified<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Site.php.html#7\">App\\Services\\Search\\Filters\\Site<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Testimonialised.php.html#7\">App\\Services\\Search\\Filters\\Testimonialised<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Title.php.html#7\">App\\Services\\Search\\Filters\\Title<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Translated.php.html#7\">App\\Services\\Search\\Filters\\Translated<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/TranslationTitle.php.html#7\">App\\Services\\Search\\Filters\\TranslationTitle<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/UpdatedAfter.php.html#7\">App\\Services\\Search\\Filters\\UpdatedAfter<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/UpdatedBefore.php.html#7\">App\\Services\\Search\\Filters\\UpdatedBefore<\/a>"],[0,1,"<a href=\"Services\/Search\/GoogleReviewSearch.php.html#8\">App\\Services\\Search\\GoogleReviewSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/ListingSearch.php.html#8\">App\\Services\\Search\\ListingSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/OfferSearch.php.html#8\">App\\Services\\Search\\OfferSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/PostSearch.php.html#8\">App\\Services\\Search\\PostSearch<\/a>"],[0,1,"<a href=\"Services\/Search\/ReservationSearch.php.html#8\">App\\Services\\Search\\ReservationSearch<\/a>"],[0,6,"<a href=\"Services\/Search\/Search.php.html#9\">App\\Services\\Search\\Search<\/a>"],[0,2,"<a href=\"Services\/Validation\/AccessoryValidator.php.html#13\">App\\Services\\Validation\\AccessoryValidator<\/a>"],[0,1,"<a href=\"Services\/Validation\/CouponValidator.php.html#7\">App\\Services\\Validation\\CouponValidator<\/a>"],[0,2,"<a href=\"Services\/Validation\/LocationValidator.php.html#13\">App\\Services\\Validation\\LocationValidator<\/a>"],[100,0,"<a href=\"Services\/Validation\/QuoteValidator.php.html#12\">App\\Services\\Validation\\QuoteValidator<\/a>"],[100,0,"<a href=\"Services\/Validation\/RepeatingClientValidator.php.html#12\">App\\Services\\Validation\\RepeatingClientValidator<\/a>"],[0,1,"<a href=\"Services\/Validation\/ReservationValidator.php.html#13\">App\\Services\\Validation\\ReservationValidator<\/a>"],[0,2,"<a href=\"Services\/Validation\/ValidationException.php.html#5\">App\\Services\\Validation\\ValidationException<\/a>"],[0,4,"<a href=\"Services\/Validation\/Validator.php.html#5\">App\\Services\\Validation\\Validator<\/a>"],[100,0,"<a href=\"Site.php.html#7\">App\\Site<\/a>"],[0,1,"<a href=\"Supergroup.php.html#10\">App\\Supergroup<\/a>"],[100,0,"<a href=\"SupergroupTranslation.php.html#5\">App\\SupergroupTranslation<\/a>"],[0,1,"<a href=\"Tag.php.html#9\">App\\Tag<\/a>"],[100,0,"<a href=\"User.php.html#8\">App\\User<\/a>"],[0,5,"<a href=\"View\/Composers\/BlogDetailsComposer.php.html#9\">App\\View\\Composers\\BlogDetailsComposer<\/a>"],[0,2,"<a href=\"View\/Composers\/FaqPartialComposer.php.html#10\">App\\View\\Composers\\FaqPartialComposer<\/a>"],[0,4,"<a href=\"View\/Composers\/SearchBucketComposer.php.html#11\">App\\View\\Composers\\SearchBucketComposer<\/a>"],[0,1,"<a href=\"View\/Composers\/TestimonialPartialComposer.php.html#11\">App\\View\\Composers\\TestimonialPartialComposer<\/a>"],[100,0,"<a href=\"Services\/DateHandling\/DateableTrait.php.html#6\">App\\Services\\DateHandling\\DateableTrait<\/a>"],[0,4,"<a href=\"Services\/ImageHandling\/ImageableTrait.php.html#17\">App\\Services\\ImageHandling\\ImageableTrait<\/a>"],[0,1,"<a href=\"Services\/PhotoHandling\/PhotoableTrait.php.html#7\">App\\Services\\PhotoHandling\\PhotoableTrait<\/a>"],[0,3,"<a href=\"Traits\/AccessorsMutators\/CustomerAccessorsMutators.php.html#7\">App\\Traits\\AccessorsMutators\\CustomerAccessorsMutators<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/FeedbackAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\FeedbackAccessorsMutators<\/a>"],[100,0,"<a href=\"Traits\/AccessorsMutators\/GoogleReviewAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\GoogleReviewAccessorsMutators<\/a>"],[0,10,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators<\/a>"],[0,5,"<a href=\"Traits\/AccessorsMutators\/ImageAccessorsMutators.php.html#7\">App\\Traits\\AccessorsMutators\\ImageAccessorsMutators<\/a>"],[0,8,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#9\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators<\/a>"],[0,5,"<a href=\"Traits\/AccessorsMutators\/LocationAccessorsMutators.php.html#7\">App\\Traits\\AccessorsMutators\\LocationAccessorsMutators<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/OfferAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\OfferAccessorsMutators<\/a>"],[0,7,"<a href=\"Traits\/AccessorsMutators\/PhotoAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\PhotoAccessorsMutators<\/a>"],[0,6,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#9\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators<\/a>"],[100,0,"<a href=\"Traits\/AccessorsMutators\/PostAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\PostAccessorsMutators<\/a>"],[0,9,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#8\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators<\/a>"],[0,17,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#10\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators<\/a>"],[0,8,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#11\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators<\/a>"],[100,0,"<a href=\"Traits\/AccessorsMutators\/SupergroupAccessorsMutators.php.html#5\">App\\Traits\\AccessorsMutators\\SupergroupAccessorsMutators<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/CategoryRelationships.php.html#5\">App\\Traits\\Relationships\\CategoryRelationships<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/CustomerRelationships.php.html#5\">App\\Traits\\Relationships\\CustomerRelationships<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/FeedbackRelationships.php.html#5\">App\\Traits\\Relationships\\FeedbackRelationships<\/a>"],[0,3,"<a href=\"Traits\/Relationships\/GoogleReviewRelationships.php.html#9\">App\\Traits\\Relationships\\GoogleReviewRelationships<\/a>"],[0,4,"<a href=\"Traits\/Relationships\/GroupRelationships.php.html#8\">App\\Traits\\Relationships\\GroupRelationships<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/ImageRelationships.php.html#5\">App\\Traits\\Relationships\\ImageRelationships<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LanguageRelationships.php.html#7\">App\\Traits\\Relationships\\LanguageRelationships<\/a>"],[0,10,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#12\">App\\Traits\\Relationships\\ListingRelationships<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/LocalisedMotifRelationships.php.html#8\">App\\Traits\\Relationships\\LocalisedMotifRelationships<\/a>"],[0,4,"<a href=\"Traits\/Relationships\/LocationRelationships.php.html#7\">App\\Traits\\Relationships\\LocationRelationships<\/a>"],[0,4,"<a href=\"Traits\/Relationships\/MotifRelationships.php.html#10\">App\\Traits\\Relationships\\MotifRelationships<\/a>"],[0,7,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#14\">App\\Traits\\Relationships\\OfferRelationships<\/a>"],[0,4,"<a href=\"Traits\/Relationships\/PeriodRelationships.php.html#8\">App\\Traits\\Relationships\\PeriodRelationships<\/a>"],[100,0,"<a href=\"Traits\/Relationships\/PhotoRelationships.php.html#5\">App\\Traits\\Relationships\\PhotoRelationships<\/a>"],[0,13,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#12\">App\\Traits\\Relationships\\PostRelationships<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/PostTranslationRelationships.php.html#7\">App\\Traits\\Relationships\\PostTranslationRelationships<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/QuoteRelationships.php.html#5\">App\\Traits\\Relationships\\QuoteRelationships<\/a>"],[0,4,"<a href=\"Traits\/Relationships\/ReservationRelationships.php.html#9\">App\\Traits\\Relationships\\ReservationRelationships<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/SupergroupRelationships.php.html#10\">App\\Traits\\Relationships\\SupergroupRelationships<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/TagRelationships.php.html#8\">App\\Traits\\Relationships\\TagRelationships<\/a>"],[0,4,"<a href=\"Traits\/Scopes\/GoogleReviewScopes.php.html#5\">App\\Traits\\Scopes\\GoogleReviewScopes<\/a>"],[0,2,"<a href=\"Traits\/Scopes\/ListingScopes.php.html#5\">App\\Traits\\Scopes\\ListingScopes<\/a>"],[0,5,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#5\">App\\Traits\\Scopes\\LocalisableScopes<\/a>"],[0,2,"<a href=\"Traits\/Scopes\/PostScopes.php.html#5\">App\\Traits\\Scopes\\PostScopes<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/ReservationScopes.php.html#5\">App\\Traits\\Scopes\\ReservationScopes<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Accessory.php.html#28\">App\\Accessory::performCustomCreationTasks<\/a>"],[0,2,"<a href=\"Accessory.php.html#45\">App\\Accessory::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Accessory.php.html#60\">App\\Accessory::delete<\/a>"],[0,1,"<a href=\"BaseModel.php.html#14\">App\\BaseModel::create<\/a>"],[0,1,"<a href=\"BaseModel.php.html#36\">App\\BaseModel::validateAndUpdate<\/a>"],[0,2,"<a href=\"BaseModel.php.html#47\">App\\BaseModel::performCreationValidation<\/a>"],[0,2,"<a href=\"BaseModel.php.html#65\">App\\BaseModel::performUpdateValidation<\/a>"],[0,1,"<a href=\"BaseModel.php.html#85\">App\\BaseModel::performParentUpdate<\/a>"],[0,0,"<a href=\"BaseModel.php.html#99\">App\\BaseModel::performCustomCreationTasks<\/a>"],[0,0,"<a href=\"BaseModel.php.html#109\">App\\BaseModel::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"BaseModel.php.html#119\">App\\BaseModel::performCustomPostCreationTasks<\/a>"],[0,1,"<a href=\"Category.php.html#22\">App\\Category::performCustomCreationTasks<\/a>"],[0,2,"<a href=\"Category.php.html#33\">App\\Category::performCustomPostCreationTasks<\/a>"],[0,2,"<a href=\"Category.php.html#47\">App\\Category::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Category.php.html#58\">App\\Category::listings<\/a>"],[0,1,"<a href=\"Coupon.php.html#28\">App\\Coupon::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"Coupon.php.html#38\">App\\Coupon::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Coupon.php.html#49\">App\\Coupon::getDiscountPercentageAttribute<\/a>"],[0,1,"<a href=\"Coupon.php.html#63\">App\\Coupon::scopeEurodollarValid<\/a>"],[0,1,"<a href=\"Coupon.php.html#75\">App\\Coupon::scopeEurodollarPromoted<\/a>"],[0,1,"<a href=\"Coupon.php.html#88\">App\\Coupon::scopeCretanrentalsValid<\/a>"],[0,1,"<a href=\"Coupon.php.html#100\">App\\Coupon::scopeCretanrentalsPromoted<\/a>"],[0,1,"<a href=\"Customer.php.html#29\">App\\Customer::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"Customer.php.html#40\">App\\Customer::performCustomPostCreationTasks<\/a>"],[0,1,"<a href=\"Customer.php.html#50\">App\\Customer::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Feedback.php.html#31\">App\\Feedback::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"Feedback.php.html#84\">App\\Feedback::performCustomUpdateTasks<\/a>"],[0,3,"<a href=\"Helpers\/ChangePortal.php.html#11\">App\\Helpers\\ChangePortal::LoadPortal<\/a>"],[100,2,"<a href=\"Helpers\/TestHelper.php.html#10\">App\\Helpers\\TestHelper::calculatePercentage<\/a>"],[100,1,"<a href=\"Helpers\/TestHelper.php.html#22\">App\\Helpers\\TestHelper::formatCurrency<\/a>"],[100,1,"<a href=\"Helpers\/TestHelper.php.html#30\">App\\Helpers\\TestHelper::isValidEmail<\/a>"],[100,2,"<a href=\"Helpers\/TestHelper.php.html#38\">App\\Helpers\\TestHelper::generateRandomString<\/a>"],[100,3,"<a href=\"Helpers\/TestHelper.php.html#53\">App\\Helpers\\TestHelper::arrayToCsv<\/a>"],[0,3,"<a href=\"Helpers\/TimeRange.php.html#14\">App\\Helpers\\TimeRange::create_time_range<\/a>"],[0,1,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#17\">App\\Http\\Controllers\\AccessoriesController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#32\">App\\Http\\Controllers\\AccessoriesController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#43\">App\\Http\\Controllers\\AccessoriesController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#70\">App\\Http\\Controllers\\AccessoriesController::edit<\/a>"],[0,3,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#87\">App\\Http\\Controllers\\AccessoriesController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/AccessoriesController.php.html#114\">App\\Http\\Controllers\\AccessoriesController::destroy<\/a>"],[0,1,"<a href=\"Http\/Controllers\/Auth\/LoginController.php.html#38\">App\\Http\\Controllers\\Auth\\LoginController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/Auth\/LoginController.php.html#43\">App\\Http\\Controllers\\Auth\\LoginController::username<\/a>"],[0,3,"<a href=\"Http\/Controllers\/Auth\/LoginController.php.html#48\">App\\Http\\Controllers\\Auth\\LoginController::authenticated<\/a>"],[0,1,"<a href=\"Http\/Controllers\/Auth\/TwoFactorController.php.html#16\">App\\Http\\Controllers\\Auth\\TwoFactorController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/Auth\/TwoFactorController.php.html#21\">App\\Http\\Controllers\\Auth\\TwoFactorController::index<\/a>"],[0,2,"<a href=\"Http\/Controllers\/Auth\/TwoFactorController.php.html#26\">App\\Http\\Controllers\\Auth\\TwoFactorController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/Auth\/TwoFactorController.php.html#46\">App\\Http\\Controllers\\Auth\\TwoFactorController::resend<\/a>"],[0,2,"<a href=\"Http\/Controllers\/AuthController.php.html#15\">App\\Http\\Controllers\\AuthController::showLogin<\/a>"],[0,3,"<a href=\"Http\/Controllers\/AuthController.php.html#30\">App\\Http\\Controllers\\AuthController::handleLogin<\/a>"],[0,1,"<a href=\"Http\/Controllers\/BookingsController.php.html#17\">App\\Http\\Controllers\\BookingsController::getCurrentReservationData<\/a>"],[0,1,"<a href=\"Http\/Controllers\/BookingsController.php.html#34\">App\\Http\\Controllers\\BookingsController::showBooking<\/a>"],[0,1,"<a href=\"Http\/Controllers\/BookingsController.php.html#57\">App\\Http\\Controllers\\BookingsController::handleBooking<\/a>"],[0,8,"<a href=\"Http\/Controllers\/BookingsController.php.html#80\">App\\Http\\Controllers\\BookingsController::fleetIndex<\/a>"],[0,13,"<a href=\"Http\/Controllers\/BookingsController.php.html#172\">App\\Http\\Controllers\\BookingsController::initialSetupOfCookie<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CategoriesController.php.html#17\">App\\Http\\Controllers\\CategoriesController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CategoriesController.php.html#33\">App\\Http\\Controllers\\CategoriesController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/CategoriesController.php.html#46\">App\\Http\\Controllers\\CategoriesController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CategoriesController.php.html#71\">App\\Http\\Controllers\\CategoriesController::edit<\/a>"],[0,5,"<a href=\"Http\/Controllers\/CategoriesController.php.html#86\">App\\Http\\Controllers\\CategoriesController::update<\/a>"],[0,2,"<a href=\"Http\/Controllers\/CategoriesController.php.html#116\">App\\Http\\Controllers\\CategoriesController::destroy<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ContactController.php.html#28\">App\\Http\\Controllers\\ContactController::showContact<\/a>"],[0,5,"<a href=\"Http\/Controllers\/ContactController.php.html#73\">App\\Http\\Controllers\\ContactController::handleContact<\/a>"],[0,2,"<a href=\"Http\/Controllers\/ContactController.php.html#151\">App\\Http\\Controllers\\ContactController::handleNewsletter<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ContactController.php.html#176\">App\\Http\\Controllers\\ContactController::getNewslettersIndex<\/a>"],[0,6,"<a href=\"Http\/Controllers\/Controller.php.html#32\">App\\Http\\Controllers\\Controller::__construct<\/a>"],[0,2,"<a href=\"Http\/Controllers\/Controller.php.html#132\">App\\Http\\Controllers\\Controller::setupLayout<\/a>"],[0,2,"<a href=\"Http\/Controllers\/Controller.php.html#149\">App\\Http\\Controllers\\Controller::overwriteOpengraph<\/a>"],[0,8,"<a href=\"Http\/Controllers\/Controller.php.html#165\">App\\Http\\Controllers\\Controller::popup<\/a>"],[0,2,"<a href=\"Http\/Controllers\/CouponsController.php.html#21\">App\\Http\\Controllers\\CouponsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CouponsController.php.html#44\">App\\Http\\Controllers\\CouponsController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/CouponsController.php.html#59\">App\\Http\\Controllers\\CouponsController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CouponsController.php.html#88\">App\\Http\\Controllers\\CouponsController::show<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CouponsController.php.html#100\">App\\Http\\Controllers\\CouponsController::edit<\/a>"],[0,3,"<a href=\"Http\/Controllers\/CouponsController.php.html#118\">App\\Http\\Controllers\\CouponsController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/CouponsController.php.html#150\">App\\Http\\Controllers\\CouponsController::destroy<\/a>"],[0,2,"<a href=\"Http\/Controllers\/CustomersController.php.html#14\">App\\Http\\Controllers\\CustomersController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/DashboardController.php.html#16\">App\\Http\\Controllers\\DashboardController::getIndex<\/a>"],[0,1,"<a href=\"Http\/Controllers\/FeedController.php.html#12\">App\\Http\\Controllers\\FeedController::__construct<\/a>"],[0,4,"<a href=\"Http\/Controllers\/FeedController.php.html#25\">App\\Http\\Controllers\\FeedController::exportCarsCsv<\/a>"],[0,5,"<a href=\"Http\/Controllers\/FeedbackController.php.html#20\">App\\Http\\Controllers\\FeedbackController::create<\/a>"],[0,8,"<a href=\"Http\/Controllers\/FeedbackController.php.html#59\">App\\Http\\Controllers\\FeedbackController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/FeedbackController.php.html#148\">App\\Http\\Controllers\\FeedbackController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/FeedbackController.php.html#164\">App\\Http\\Controllers\\FeedbackController::show<\/a>"],[0,1,"<a href=\"Http\/Controllers\/HomeController.php.html#12\">App\\Http\\Controllers\\HomeController::__construct<\/a>"],[0,2,"<a href=\"Http\/Controllers\/HomeController.php.html#20\">App\\Http\\Controllers\\HomeController::getIndex<\/a>"],[0,5,"<a href=\"Http\/Controllers\/ImageController.php.html#18\">App\\Http\\Controllers\\ImageController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ImageController.php.html#32\">App\\Http\\Controllers\\ImageController::postUpload<\/a>"],[0,2,"<a href=\"Http\/Controllers\/ImageController.php.html#40\">App\\Http\\Controllers\\ImageController::deleteUpload<\/a>"],[0,4,"<a href=\"Http\/Controllers\/ImageController.php.html#55\">App\\Http\\Controllers\\ImageController::getListingImages<\/a>"],[0,4,"<a href=\"Http\/Controllers\/ImageController.php.html#86\">App\\Http\\Controllers\\ImageController::getLocationImages<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ListingsController.php.html#24\">App\\Http\\Controllers\\ListingsController::__construct<\/a>"],[0,11,"<a href=\"Http\/Controllers\/ListingsController.php.html#36\">App\\Http\\Controllers\\ListingsController::index<\/a>"],[0,14,"<a href=\"Http\/Controllers\/ListingsController.php.html#255\">App\\Http\\Controllers\\ListingsController::show<\/a>"],[0,12,"<a href=\"Http\/Controllers\/ListingsController.php.html#477\">App\\Http\\Controllers\\ListingsController::getPrice<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ListingsController.php.html#550\">App\\Http\\Controllers\\ListingsController::handleUnavailable<\/a>"],[0,1,"<a href=\"Http\/Controllers\/LocationsController.php.html#18\">App\\Http\\Controllers\\LocationsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/LocationsController.php.html#34\">App\\Http\\Controllers\\LocationsController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/LocationsController.php.html#47\">App\\Http\\Controllers\\LocationsController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/LocationsController.php.html#77\">App\\Http\\Controllers\\LocationsController::edit<\/a>"],[0,3,"<a href=\"Http\/Controllers\/LocationsController.php.html#93\">App\\Http\\Controllers\\LocationsController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/LocationsController.php.html#123\">App\\Http\\Controllers\\LocationsController::destroy<\/a>"],[0,3,"<a href=\"Http\/Controllers\/OffersController.php.html#23\">App\\Http\\Controllers\\OffersController::show<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#10\">App\\Http\\Controllers\\PagesController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#21\">App\\Http\\Controllers\\PagesController::about<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#61\">App\\Http\\Controllers\\PagesController::offers<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#70\">App\\Http\\Controllers\\PagesController::crete<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#78\">App\\Http\\Controllers\\PagesController::services<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#118\">App\\Http\\Controllers\\PagesController::branches<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PagesController.php.html#127\">App\\Http\\Controllers\\PagesController::faq<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#189\">App\\Http\\Controllers\\PagesController::policy<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#229\">App\\Http\\Controllers\\PagesController::privacy<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#260\">App\\Http\\Controllers\\PagesController::safety<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PagesController.php.html#293\">App\\Http\\Controllers\\PagesController::insurance<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PlacesController.php.html#32\">App\\Http\\Controllers\\PlacesController::__construct<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PlacesController.php.html#74\">App\\Http\\Controllers\\PlacesController::heraklion<\/a>"],[0,3,"<a href=\"Http\/Controllers\/PlacesController.php.html#184\">App\\Http\\Controllers\\PlacesController::heraklion_airport<\/a>"],[0,3,"<a href=\"Http\/Controllers\/PlacesController.php.html#298\">App\\Http\\Controllers\\PlacesController::chania_airport<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PlacesController.php.html#411\">App\\Http\\Controllers\\PlacesController::chania<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PlacesController.php.html#522\">App\\Http\\Controllers\\PlacesController::rethymno<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PlacesController.php.html#634\">App\\Http\\Controllers\\PlacesController::agios_nikolaos<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PlacesController.php.html#749\">App\\Http\\Controllers\\PlacesController::setDefaultLocation<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PopupController.php.html#18\">App\\Http\\Controllers\\PopupController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PopupController.php.html#33\">App\\Http\\Controllers\\PopupController::create<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PopupController.php.html#48\">App\\Http\\Controllers\\PopupController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PopupController.php.html#72\">App\\Http\\Controllers\\PopupController::edit<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PopupController.php.html#90\">App\\Http\\Controllers\\PopupController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PopupController.php.html#114\">App\\Http\\Controllers\\PopupController::destroy<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PopupController.php.html#130\">App\\Http\\Controllers\\PopupController::return_bytes<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PostsController.php.html#16\">App\\Http\\Controllers\\PostsController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PostsController.php.html#28\">App\\Http\\Controllers\\PostsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PostsController.php.html#37\">App\\Http\\Controllers\\PostsController::indexWithTags<\/a>"],[0,6,"<a href=\"Http\/Controllers\/PostsController.php.html#48\">App\\Http\\Controllers\\PostsController::show<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PostsController.php.html#139\">App\\Http\\Controllers\\PostsController::handleIndex<\/a>"],[0,1,"<a href=\"Http\/Controllers\/PricingController.php.html#20\">App\\Http\\Controllers\\PricingController::index<\/a>"],[0,2,"<a href=\"Http\/Controllers\/PricingController.php.html#37\">App\\Http\\Controllers\\PricingController::edit<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PricingController.php.html#73\">App\\Http\\Controllers\\PricingController::update<\/a>"],[0,4,"<a href=\"Http\/Controllers\/PricingController.php.html#113\">App\\Http\\Controllers\\PricingController::availabilityUpdate<\/a>"],[0,2,"<a href=\"Http\/Controllers\/QuotesController.php.html#26\">App\\Http\\Controllers\\QuotesController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/QuotesController.php.html#47\">App\\Http\\Controllers\\QuotesController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/QuotesController.php.html#57\">App\\Http\\Controllers\\QuotesController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/QuotesController.php.html#112\">App\\Http\\Controllers\\QuotesController::edit<\/a>"],[0,2,"<a href=\"Http\/Controllers\/QuotesController.php.html#132\">App\\Http\\Controllers\\QuotesController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/QuotesController.php.html#156\">App\\Http\\Controllers\\QuotesController::destroy<\/a>"],[0,2,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#22\">App\\Http\\Controllers\\RepeatingClientsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#44\">App\\Http\\Controllers\\RepeatingClientsController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#55\">App\\Http\\Controllers\\RepeatingClientsController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#95\">App\\Http\\Controllers\\RepeatingClientsController::show<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#114\">App\\Http\\Controllers\\RepeatingClientsController::edit<\/a>"],[0,2,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#126\">App\\Http\\Controllers\\RepeatingClientsController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RepeatingClientsController.php.html#151\">App\\Http\\Controllers\\RepeatingClientsController::destroy<\/a>"],[0,16,"<a href=\"Http\/Controllers\/ReservationsController.php.html#27\">App\\Http\\Controllers\\ReservationsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReservationsController.php.html#160\">App\\Http\\Controllers\\ReservationsController::create<\/a>"],[0,3,"<a href=\"Http\/Controllers\/ReservationsController.php.html#171\">App\\Http\\Controllers\\ReservationsController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReservationsController.php.html#246\">App\\Http\\Controllers\\ReservationsController::show<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReservationsController.php.html#266\">App\\Http\\Controllers\\ReservationsController::edit<\/a>"],[0,2,"<a href=\"Http\/Controllers\/ReservationsController.php.html#278\">App\\Http\\Controllers\\ReservationsController::update<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReservationsController.php.html#299\">App\\Http\\Controllers\\ReservationsController::destroy<\/a>"],[0,2,"<a href=\"Http\/Controllers\/ReservationsController.php.html#319\">App\\Http\\Controllers\\ReservationsController::confirm<\/a>"],[0,4,"<a href=\"Http\/Controllers\/ReservationsController.php.html#343\">App\\Http\\Controllers\\ReservationsController::verify<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReviewsController.php.html#8\">App\\Http\\Controllers\\ReviewsController::__construct<\/a>"],[0,1,"<a href=\"Http\/Controllers\/ReviewsController.php.html#16\">App\\Http\\Controllers\\ReviewsController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/RobotsController.php.html#6\">App\\Http\\Controllers\\RobotsController::robots<\/a>"],[0,1,"<a href=\"Http\/Controllers\/SearchController.php.html#13\">App\\Http\\Controllers\\SearchController::__construct<\/a>"],[0,2,"<a href=\"Http\/Controllers\/SearchController.php.html#21\">App\\Http\\Controllers\\SearchController::index<\/a>"],[0,1,"<a href=\"Http\/Controllers\/SessionsController.php.html#14\">App\\Http\\Controllers\\SessionsController::store<\/a>"],[0,1,"<a href=\"Http\/Controllers\/SessionsController.php.html#28\">App\\Http\\Controllers\\SessionsController::destroyAll<\/a>"],[0,1,"<a href=\"Http\/Controllers\/TestController.php.html#12\">App\\Http\\Controllers\\TestController::offerEmail<\/a>"],[0,1,"<a href=\"Http\/Controllers\/TestController.php.html#33\">App\\Http\\Controllers\\TestController::deepl<\/a>"],[0,1,"<a href=\"Http\/Controllers\/TestController.php.html#48\">App\\Http\\Controllers\\TestController::multiMail<\/a>"],[0,1,"<a href=\"Http\/Requests\/AvailabilityRequest.php.html#14\">App\\Http\\Requests\\AvailabilityRequest::authorize<\/a>"],[0,1,"<a href=\"Http\/Requests\/AvailabilityRequest.php.html#24\">App\\Http\\Requests\\AvailabilityRequest::rules<\/a>"],[0,1,"<a href=\"Http\/Requests\/ContactRequest.php.html#14\">App\\Http\\Requests\\ContactRequest::authorize<\/a>"],[0,1,"<a href=\"Http\/Requests\/ContactRequest.php.html#24\">App\\Http\\Requests\\ContactRequest::rules<\/a>"],[0,4,"<a href=\"Http\/Requests\/ContactRequest.php.html#31\">App\\Http\\Requests\\ContactRequest::sanitize<\/a>"],[0,1,"<a href=\"Http\/Requests\/PricingRequest.php.html#14\">App\\Http\\Requests\\PricingRequest::authorize<\/a>"],[0,1,"<a href=\"Http\/Requests\/PricingRequest.php.html#24\">App\\Http\\Requests\\PricingRequest::rules<\/a>"],[0,1,"<a href=\"Http\/Requests\/ReservationRequest.php.html#14\">App\\Http\\Requests\\ReservationRequest::authorize<\/a>"],[0,1,"<a href=\"Http\/Requests\/ReservationRequest.php.html#24\">App\\Http\\Requests\\ReservationRequest::rules<\/a>"],[0,8,"<a href=\"Http\/Requests\/ReservationRequest.php.html#31\">App\\Http\\Requests\\ReservationRequest::sanitize<\/a>"],[0,1,"<a href=\"Image.php.html#33\">App\\Image::performCustomCreationTasks<\/a>"],[0,3,"<a href=\"Image.php.html#44\">App\\Image::performCustomPostCreationTasks<\/a>"],[0,3,"<a href=\"Image.php.html#62\">App\\Image::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Jobs\/SendDuplicateReservationReminder.php.html#23\">App\\Jobs\\SendDuplicateReservationReminder::handle<\/a>"],[0,1,"<a href=\"Jobs\/SendOfferNotification.php.html#30\">App\\Jobs\\SendOfferNotification::__construct<\/a>"],[0,2,"<a href=\"Jobs\/SendOfferNotification.php.html#40\">App\\Jobs\\SendOfferNotification::handle<\/a>"],[0,1,"<a href=\"Jobs\/SendReservationFeedback.php.html#32\">App\\Jobs\\SendReservationFeedback::__construct<\/a>"],[0,6,"<a href=\"Jobs\/SendReservationFeedback.php.html#42\">App\\Jobs\\SendReservationFeedback::handle<\/a>"],[0,1,"<a href=\"Jobs\/SendReservationReminder.php.html#34\">App\\Jobs\\SendReservationReminder::__construct<\/a>"],[0,3,"<a href=\"Jobs\/SendReservationReminder.php.html#44\">App\\Jobs\\SendReservationReminder::handle<\/a>"],[0,1,"<a href=\"Jobs\/SendReservationVerifyReminder.php.html#32\">App\\Jobs\\SendReservationVerifyReminder::__construct<\/a>"],[0,2,"<a href=\"Jobs\/SendReservationVerifyReminder.php.html#42\">App\\Jobs\\SendReservationVerifyReminder::handle<\/a>"],[0,1,"<a href=\"Listing.php.html#77\">App\\Listing::registerMediaConversions<\/a>"],[0,1,"<a href=\"Listing.php.html#90\">App\\Listing::sluggable<\/a>"],[0,1,"<a href=\"Listing.php.html#99\">App\\Listing::toSitemapTag<\/a>"],[0,1,"<a href=\"Livewire\/Frontend\/ReviewsPage.php.html#19\">App\\Livewire\\Frontend\\ReviewsPage::loadMore<\/a>"],[0,3,"<a href=\"Livewire\/Frontend\/ReviewsPage.php.html#24\">App\\Livewire\\Frontend\\ReviewsPage::render<\/a>"],[0,2,"<a href=\"Location.php.html#43\">App\\Location::performCustomCreationTasks<\/a>"],[0,6,"<a href=\"Location.php.html#61\">App\\Location::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Location.php.html#88\">App\\Location::reviews<\/a>"],[0,5,"<a href=\"Location.php.html#100\">App\\Location::performCustomPostCreationTasks<\/a>"],[0,1,"<a href=\"Location.php.html#119\">App\\Location::delete<\/a>"],[0,1,"<a href=\"Models\/User.php.html#55\">App\\Models\\User::generateTwoFactorCode<\/a>"],[0,1,"<a href=\"Models\/User.php.html#66\">App\\Models\\User::resetTwoFactorCode<\/a>"],[0,1,"<a href=\"Notifications\/TwoFactorCode.php.html#19\">App\\Notifications\\TwoFactorCode::__construct<\/a>"],[0,1,"<a href=\"Notifications\/TwoFactorCode.php.html#30\">App\\Notifications\\TwoFactorCode::via<\/a>"],[0,1,"<a href=\"Notifications\/TwoFactorCode.php.html#41\">App\\Notifications\\TwoFactorCode::toMail<\/a>"],[0,1,"<a href=\"Notifications\/TwoFactorCode.php.html#58\">App\\Notifications\\TwoFactorCode::toArray<\/a>"],[0,13,"<a href=\"Offer.php.html#44\">App\\Offer::isReady<\/a>"],[0,2,"<a href=\"Popup.php.html#26\">App\\Popup::performCustomCreationTasks<\/a>"],[0,2,"<a href=\"Popup.php.html#42\">App\\Popup::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Popup.php.html#58\">App\\Popup::delete<\/a>"],[0,1,"<a href=\"Post.php.html#47\">App\\Post::registerMediaConversions<\/a>"],[0,1,"<a href=\"Post.php.html#57\">App\\Post::excerpt<\/a>"],[0,1,"<a href=\"Post.php.html#68\">App\\Post::toSitemapTag<\/a>"],[0,1,"<a href=\"PostTranslation.php.html#30\">App\\PostTranslation::sluggable<\/a>"],[0,1,"<a href=\"PostTranslation.php.html#41\">App\\PostTranslation::getSearchResult<\/a>"],[0,1,"<a href=\"Pricing.php.html#23\">App\\Pricing::booted<\/a>"],[100,1,"<a href=\"Providers\/AppServiceProvider.php.html#14\">App\\Providers\\AppServiceProvider::register<\/a>"],[70,1,"<a href=\"Providers\/AppServiceProvider.php.html#22\">App\\Providers\\AppServiceProvider::boot<\/a>"],[66.66666666666666,1,"<a href=\"Providers\/AuthServiceProvider.php.html#22\">App\\Providers\\AuthServiceProvider::boot<\/a>"],[0,1,"<a href=\"Providers\/BroadcastServiceProvider.php.html#13\">App\\Providers\\BroadcastServiceProvider::boot<\/a>"],[0,1,"<a href=\"Providers\/BusServiceProvider.php.html#14\">App\\Providers\\BusServiceProvider::boot<\/a>"],[0,1,"<a href=\"Providers\/BusServiceProvider.php.html#29\">App\\Providers\\BusServiceProvider::register<\/a>"],[0,1,"<a href=\"Providers\/ConfigServiceProvider.php.html#16\">App\\Providers\\ConfigServiceProvider::register<\/a>"],[100,1,"<a href=\"Providers\/EventServiceProvider.php.html#26\">App\\Providers\\EventServiceProvider::boot<\/a>"],[100,1,"<a href=\"Providers\/EventServiceProvider.php.html#34\">App\\Providers\\EventServiceProvider::shouldDiscoverEvents<\/a>"],[100,1,"<a href=\"Providers\/RepositoryServiceProvider.php.html#12\">App\\Providers\\RepositoryServiceProvider::boot<\/a>"],[100,1,"<a href=\"Providers\/RepositoryServiceProvider.php.html#22\">App\\Providers\\RepositoryServiceProvider::register<\/a>"],[100,1,"<a href=\"Providers\/ViewServiceProvider.php.html#19\">App\\Providers\\ViewServiceProvider::register<\/a>"],[100,1,"<a href=\"Providers\/ViewServiceProvider.php.html#29\">App\\Providers\\ViewServiceProvider::boot<\/a>"],[0,9,"<a href=\"Quote.php.html#31\">App\\Quote::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"Quote.php.html#84\">App\\Quote::performCustomUpdateTasks<\/a>"],[0,2,"<a href=\"RepeatingClient.php.html#25\">App\\RepeatingClient::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"RepeatingClient.php.html#34\">App\\RepeatingClient::performCustomUpdateTasks<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#17\">App\\Repositories\\Eloquent\\Repository::__construct<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#26\">App\\Repositories\\Eloquent\\Repository::all<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#36\">App\\Repositories\\Eloquent\\Repository::lists<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#46\">App\\Repositories\\Eloquent\\Repository::paginate<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#55\">App\\Repositories\\Eloquent\\Repository::create<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#66\">App\\Repositories\\Eloquent\\Repository::update<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#77\">App\\Repositories\\Eloquent\\Repository::delete<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#87\">App\\Repositories\\Eloquent\\Repository::find<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#98\">App\\Repositories\\Eloquent\\Repository::findBy<\/a>"],[0,2,"<a href=\"Repositories\/Eloquent\/Repository.php.html#107\">App\\Repositories\\Eloquent\\Repository::orderBy<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#121\">App\\Repositories\\Eloquent\\Repository::findOrFail<\/a>"],[0,1,"<a href=\"Repositories\/Eloquent\/Repository.php.html#133\">App\\Repositories\\Eloquent\\Repository::where<\/a>"],[0,1,"<a href=\"Repositories\/ImageRepository.php.html#21\">App\\Repositories\\ImageRepository::__construct<\/a>"],[0,1,"<a href=\"Repositories\/ImageRepository.php.html#26\">App\\Repositories\\ImageRepository::getConfigFullSize<\/a>"],[0,2,"<a href=\"Repositories\/ImageRepository.php.html#31\">App\\Repositories\\ImageRepository::setConfigFullSize<\/a>"],[0,1,"<a href=\"Repositories\/ImageRepository.php.html#39\">App\\Repositories\\ImageRepository::getConfigIconSize<\/a>"],[0,2,"<a href=\"Repositories\/ImageRepository.php.html#44\">App\\Repositories\\ImageRepository::setConfigIconSize<\/a>"],[0,4,"<a href=\"Repositories\/ImageRepository.php.html#52\">App\\Repositories\\ImageRepository::upload<\/a>"],[0,2,"<a href=\"Repositories\/ImageRepository.php.html#103\">App\\Repositories\\ImageRepository::createUniqueFilename<\/a>"],[0,1,"<a href=\"Repositories\/ImageRepository.php.html#121\">App\\Repositories\\ImageRepository::original<\/a>"],[0,1,"<a href=\"Repositories\/ImageRepository.php.html#132\">App\\Repositories\\ImageRepository::icon<\/a>"],[0,5,"<a href=\"Repositories\/ImageRepository.php.html#146\">App\\Repositories\\ImageRepository::delete<\/a>"],[0,4,"<a href=\"Repositories\/ImageRepository.php.html#188\">App\\Repositories\\ImageRepository::sanitize<\/a>"],[0,1,"<a href=\"Repositories\/ListingRepository.php.html#13\">App\\Repositories\\ListingRepository::__construct<\/a>"],[0,1,"<a href=\"Repositories\/ListingRepository.php.html#22\">App\\Repositories\\ListingRepository::findBySlug<\/a>"],[0,2,"<a href=\"Repositories\/ListingRepository.php.html#34\">App\\Repositories\\ListingRepository::findListings<\/a>"],[0,1,"<a href=\"Repositories\/ListingRepository.php.html#50\">App\\Repositories\\ListingRepository::getRelated<\/a>"],[0,1,"<a href=\"Repositories\/ListingRepository.php.html#65\">App\\Repositories\\ListingRepository::getSuggested<\/a>"],[0,2,"<a href=\"Repositories\/ListingRepository.php.html#86\">App\\Repositories\\ListingRepository::findByGroups<\/a>"],[0,4,"<a href=\"Repositories\/ListingRepository.php.html#102\">App\\Repositories\\ListingRepository::findByTransmission<\/a>"],[0,4,"<a href=\"Repositories\/ListingRepository.php.html#125\">App\\Repositories\\ListingRepository::findByFuel<\/a>"],[0,4,"<a href=\"Repositories\/ListingRepository.php.html#148\">App\\Repositories\\ListingRepository::findBySeats<\/a>"],[0,5,"<a href=\"Repositories\/ListingRepository.php.html#176\">App\\Repositories\\ListingRepository::getListings<\/a>"],[0,1,"<a href=\"Repositories\/PeriodRepository.php.html#12\">App\\Repositories\\PeriodRepository::__construct<\/a>"],[0,1,"<a href=\"Reservation.php.html#64\">App\\Reservation::scopeUpcoming<\/a>"],[0,1,"<a href=\"Reservation.php.html#74\">App\\Reservation::scopePending<\/a>"],[0,1,"<a href=\"Reservation.php.html#84\">App\\Reservation::scopeScheduled<\/a>"],[0,23,"<a href=\"Reservation.php.html#89\">App\\Reservation::performCustomCreationTasks<\/a>"],[0,1,"<a href=\"Reservation.php.html#380\">App\\Reservation::performCustomUpdateTasks<\/a>"],[0,3,"<a href=\"Reservation.php.html#389\">App\\Reservation::shouldSendReminder<\/a>"],[0,1,"<a href=\"Reservation.php.html#401\">App\\Reservation::handleCustomer<\/a>"],[0,5,"<a href=\"Reservation.php.html#427\">App\\Reservation::getReservationShowDataAggregatedBy<\/a>"],[0,5,"<a href=\"Reservation.php.html#467\">App\\Reservation::getReservationShowDataMonthlyAggregatedBy<\/a>"],[0,2,"<a href=\"Rules\/InvisibleRecaptchaRule.php.html#16\">App\\Rules\\InvisibleRecaptchaRule::validate<\/a>"],[0,1,"<a href=\"Services\/Offer\/BasicDiscount.php.html#59\">App\\Services\\Offer\\BasicDiscount::__construct<\/a>"],[0,4,"<a href=\"Services\/Offer\/BasicDiscount.php.html#71\">App\\Services\\Offer\\BasicDiscount::getDiscountAmount<\/a>"],[0,3,"<a href=\"Services\/Offer\/BasicDiscount.php.html#105\">App\\Services\\Offer\\BasicDiscount::getNumberOfDaysDiscountPercentage<\/a>"],[0,5,"<a href=\"Services\/Offer\/BasicDiscount.php.html#129\">App\\Services\\Offer\\BasicDiscount::getPeriodPrice<\/a>"],[0,1,"<a href=\"Services\/Offer\/CouponDiscount.php.html#22\">App\\Services\\Offer\\CouponDiscount::__construct<\/a>"],[0,3,"<a href=\"Services\/Offer\/CouponDiscount.php.html#29\">App\\Services\\Offer\\CouponDiscount::getDiscountAmount<\/a>"],[0,1,"<a href=\"Services\/Offer\/DateRange.php.html#60\">App\\Services\\Offer\\DateRange::__construct<\/a>"],[0,7,"<a href=\"Services\/Offer\/DateRange.php.html#76\">App\\Services\\Offer\\DateRange::setPeriodCounters<\/a>"],[0,1,"<a href=\"Services\/Offer\/DateRange.php.html#133\">App\\Services\\Offer\\DateRange::getPeriodCounters<\/a>"],[0,2,"<a href=\"Services\/Offer\/DateRange.php.html#143\">App\\Services\\Offer\\DateRange::getTotalDays<\/a>"],[0,2,"<a href=\"Services\/Offer\/DateRange.php.html#158\">App\\Services\\Offer\\DateRange::getExtraDayCharge<\/a>"],[0,1,"<a href=\"Services\/Offer\/DateRange.php.html#166\">App\\Services\\Offer\\DateRange::getPickupDateTime<\/a>"],[0,6,"<a href=\"Services\/Offer\/DateRange.php.html#176\">App\\Services\\Offer\\DateRange::getPeriod<\/a>"],[0,1,"<a href=\"Services\/Offer\/DateRange.php.html#223\">App\\Services\\Offer\\DateRange::getDaysIndex<\/a>"],[100,0,"<a href=\"Services\/Offer\/DiscountBehaviour.php.html#19\">App\\Services\\Offer\\DiscountBehaviour::getDiscountAmount<\/a>"],[0,1,"<a href=\"Services\/Offer\/PredefinedOffers.php.html#22\">App\\Services\\Offer\\PredefinedOffers::__construct<\/a>"],[0,9,"<a href=\"Services\/Offer\/PredefinedOffers.php.html#32\">App\\Services\\Offer\\PredefinedOffers::getPredefinedOffers<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#80\">App\\Services\\Offer\\RentalOffer::__construct<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#106\">App\\Services\\Offer\\RentalOffer::getDiscountPercentage<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#111\">App\\Services\\Offer\\RentalOffer::getDiscountAmount<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#122\">App\\Services\\Offer\\RentalOffer::getTotalDiscountPrice<\/a>"],[0,9,"<a href=\"Services\/Offer\/RentalOffer.php.html#140\">App\\Services\\Offer\\RentalOffer::getRangePrice<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#177\">App\\Services\\Offer\\RentalOffer::getBasePrice<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#187\">App\\Services\\Offer\\RentalOffer::getAccessoriesPrice<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#208\">App\\Services\\Offer\\RentalOffer::getAccessoryPrice<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#226\">App\\Services\\Offer\\RentalOffer::hasExtraDayCharge<\/a>"],[0,4,"<a href=\"Services\/Offer\/RentalOffer.php.html#235\">App\\Services\\Offer\\RentalOffer::hasExtraMilesCharge<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#250\">App\\Services\\Offer\\RentalOffer::getExtraMilesCharge<\/a>"],[0,4,"<a href=\"Services\/Offer\/RentalOffer.php.html#259\">App\\Services\\Offer\\RentalOffer::hasRemoteLocationPickupCharge<\/a>"],[0,4,"<a href=\"Services\/Offer\/RentalOffer.php.html#277\">App\\Services\\Offer\\RentalOffer::hasRemoteLocationDropoffCharge<\/a>"],[0,7,"<a href=\"Services\/Offer\/RentalOffer.php.html#291\">App\\Services\\Offer\\RentalOffer::getRemoteLocationCharge<\/a>"],[0,4,"<a href=\"Services\/Offer\/RentalOffer.php.html#332\">App\\Services\\Offer\\RentalOffer::hasAfterHoursCharge<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#368\">App\\Services\\Offer\\RentalOffer::getAfterHoursCharge<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#377\">App\\Services\\Offer\\RentalOffer::hasFuelPlanCharge<\/a>"],[0,2,"<a href=\"Services\/Offer\/RentalOffer.php.html#382\">App\\Services\\Offer\\RentalOffer::getFuelPlanCharge<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#390\">App\\Services\\Offer\\RentalOffer::hasAvailability<\/a>"],[0,6,"<a href=\"Services\/Offer\/RentalOffer.php.html#398\">App\\Services\\Offer\\RentalOffer::setAvailability<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#439\">App\\Services\\Offer\\RentalOffer::getTotalDays<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#450\">App\\Services\\Offer\\RentalOffer::getPickupLocation<\/a>"],[0,1,"<a href=\"Services\/Offer\/RentalOffer.php.html#460\">App\\Services\\Offer\\RentalOffer::getDropoffLocation<\/a>"],[0,1,"<a href=\"Services\/Offer\/StaticDiscount.php.html#66\">App\\Services\\Offer\\StaticDiscount::__construct<\/a>"],[0,3,"<a href=\"Services\/Offer\/StaticDiscount.php.html#78\">App\\Services\\Offer\\StaticDiscount::getDiscountAmount<\/a>"],[0,3,"<a href=\"Services\/Offer\/StaticDiscount.php.html#109\">App\\Services\\Offer\\StaticDiscount::getNumberOfDaysDiscountPercentage<\/a>"],[0,5,"<a href=\"Services\/Offer\/StaticDiscount.php.html#135\">App\\Services\\Offer\\StaticDiscount::calculateTotalPrice<\/a>"],[0,1,"<a href=\"Services\/Search\/ContactLeadSearch.php.html#11\">App\\Services\\Search\\ContactLeadSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/CustomerSearch.php.html#11\">App\\Services\\Search\\CustomerSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/AuthorShownName.php.html#23\">App\\Services\\Search\\Filters\\AuthorShownName::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/AuthorTitle.php.html#23\">App\\Services\\Search\\Filters\\AuthorTitle::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/CreatedAfter.php.html#23\">App\\Services\\Search\\Filters\\CreatedAfter::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/CreatedBefore.php.html#23\">App\\Services\\Search\\Filters\\CreatedBefore::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Email.php.html#23\">App\\Services\\Search\\Filters\\Email::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Enabled.php.html#23\">App\\Services\\Search\\Filters\\Enabled::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Featured.php.html#23\">App\\Services\\Search\\Filters\\Featured::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#16\">App\\Services\\Search\\Filters\\GlobalFilter::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#28\">App\\Services\\Search\\Filters\\GlobalFilter::filterEqual<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#40\">App\\Services\\Search\\Filters\\GlobalFilter::filterLike<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#52\">App\\Services\\Search\\Filters\\GlobalFilter::filterTranslationLike<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#64\">App\\Services\\Search\\Filters\\GlobalFilter::filterGTE<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#76\">App\\Services\\Search\\Filters\\GlobalFilter::filterLTE<\/a>"],[0,3,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#88\">App\\Services\\Search\\Filters\\GlobalFilter::filterYN<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#108\">App\\Services\\Search\\Filters\\GlobalFilter::filterRelatedId<\/a>"],[0,3,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#122\">App\\Services\\Search\\Filters\\GlobalFilter::filterBoolean<\/a>"],[0,3,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#143\">App\\Services\\Search\\Filters\\GlobalFilter::filterPseudoBoolean<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GlobalFilter.php.html#163\">App\\Services\\Search\\Filters\\GlobalFilter::filterTranslatedIn<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/GroupId.php.html#23\">App\\Services\\Search\\Filters\\GroupId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasCategories.php.html#23\">App\\Services\\Search\\Filters\\HasCategories::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasLocation.php.html#23\">App\\Services\\Search\\Filters\\HasLocation::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasMotifs.php.html#23\">App\\Services\\Search\\Filters\\HasMotifs::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasReservation.php.html#23\">App\\Services\\Search\\Filters\\HasReservation::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/HasTags.php.html#23\">App\\Services\\Search\\Filters\\HasTags::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Id.php.html#23\">App\\Services\\Search\\Filters\\Id::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Intercepted.php.html#23\">App\\Services\\Search\\Filters\\Intercepted::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Language.php.html#23\">App\\Services\\Search\\Filters\\Language::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/LocationId.php.html#23\">App\\Services\\Search\\Filters\\LocationId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Manufacturer.php.html#23\">App\\Services\\Search\\Filters\\Manufacturer::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Model.php.html#23\">App\\Services\\Search\\Filters\\Model::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/MotifRelatedId.php.html#23\">App\\Services\\Search\\Filters\\MotifRelatedId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Name.php.html#23\">App\\Services\\Search\\Filters\\Name::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/OfferId.php.html#23\">App\\Services\\Search\\Filters\\OfferId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Origin.php.html#23\">App\\Services\\Search\\Filters\\Origin::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PickupAfter.php.html#23\">App\\Services\\Search\\Filters\\PickupAfter::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PickupBefore.php.html#23\">App\\Services\\Search\\Filters\\PickupBefore::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Published.php.html#23\">App\\Services\\Search\\Filters\\Published::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PublishedCretanrentals.php.html#23\">App\\Services\\Search\\Filters\\PublishedCretanrentals::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/PublishedEurodollar.php.html#23\">App\\Services\\Search\\Filters\\PublishedEurodollar::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReservationId.php.html#23\">App\\Services\\Search\\Filters\\ReservationId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewId.php.html#23\">App\\Services\\Search\\Filters\\ReviewId::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewRating.php.html#23\">App\\Services\\Search\\Filters\\ReviewRating::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ReviewText.php.html#23\">App\\Services\\Search\\Filters\\ReviewText::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Sent.php.html#23\">App\\Services\\Search\\Filters\\Sent::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Show.php.html#23\">App\\Services\\Search\\Filters\\Show::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/ShowVerified.php.html#23\">App\\Services\\Search\\Filters\\ShowVerified::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Site.php.html#23\">App\\Services\\Search\\Filters\\Site::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Testimonialised.php.html#23\">App\\Services\\Search\\Filters\\Testimonialised::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Title.php.html#23\">App\\Services\\Search\\Filters\\Title::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/Translated.php.html#23\">App\\Services\\Search\\Filters\\Translated::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/TranslationTitle.php.html#23\">App\\Services\\Search\\Filters\\TranslationTitle::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/UpdatedAfter.php.html#23\">App\\Services\\Search\\Filters\\UpdatedAfter::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/Filters\/UpdatedBefore.php.html#23\">App\\Services\\Search\\Filters\\UpdatedBefore::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/GoogleReviewSearch.php.html#11\">App\\Services\\Search\\GoogleReviewSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/ListingSearch.php.html#11\">App\\Services\\Search\\ListingSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/OfferSearch.php.html#11\">App\\Services\\Search\\OfferSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/PostSearch.php.html#11\">App\\Services\\Search\\PostSearch::apply<\/a>"],[0,1,"<a href=\"Services\/Search\/ReservationSearch.php.html#11\">App\\Services\\Search\\ReservationSearch::apply<\/a>"],[100,0,"<a href=\"Services\/Search\/Search.php.html#12\">App\\Services\\Search\\Search::apply<\/a>"],[0,3,"<a href=\"Services\/Search\/Search.php.html#14\">App\\Services\\Search\\Search::applyDecoratorsFromFilters<\/a>"],[0,1,"<a href=\"Services\/Search\/Search.php.html#28\">App\\Services\\Search\\Search::createFilterDecorator<\/a>"],[0,1,"<a href=\"Services\/Search\/Search.php.html#33\">App\\Services\\Search\\Search::isValidDecorator<\/a>"],[0,1,"<a href=\"Services\/Search\/Search.php.html#38\">App\\Services\\Search\\Search::getResults<\/a>"],[0,2,"<a href=\"Services\/Validation\/AccessoryValidator.php.html#38\">App\\Services\\Validation\\AccessoryValidator::__construct<\/a>"],[0,1,"<a href=\"Services\/Validation\/CouponValidator.php.html#33\">App\\Services\\Validation\\CouponValidator::__construct<\/a>"],[0,2,"<a href=\"Services\/Validation\/LocationValidator.php.html#36\">App\\Services\\Validation\\LocationValidator::__construct<\/a>"],[0,1,"<a href=\"Services\/Validation\/ReservationValidator.php.html#22\">App\\Services\\Validation\\ReservationValidator::__construct<\/a>"],[0,1,"<a href=\"Services\/Validation\/ValidationException.php.html#22\">App\\Services\\Validation\\ValidationException::__construct<\/a>"],[0,1,"<a href=\"Services\/Validation\/ValidationException.php.html#34\">App\\Services\\Validation\\ValidationException::getErrors<\/a>"],[0,2,"<a href=\"Services\/Validation\/Validator.php.html#16\">App\\Services\\Validation\\Validator::validate<\/a>"],[0,1,"<a href=\"Services\/Validation\/Validator.php.html#33\">App\\Services\\Validation\\Validator::validateForCreation<\/a>"],[0,1,"<a href=\"Services\/Validation\/Validator.php.html#46\">App\\Services\\Validation\\Validator::validateForUpdate<\/a>"],[0,1,"<a href=\"Supergroup.php.html#35\">App\\Supergroup::sluggable<\/a>"],[0,1,"<a href=\"Tag.php.html#23\">App\\Tag::sluggable<\/a>"],[0,5,"<a href=\"View\/Composers\/BlogDetailsComposer.php.html#18\">App\\View\\Composers\\BlogDetailsComposer::compose<\/a>"],[0,2,"<a href=\"View\/Composers\/FaqPartialComposer.php.html#19\">App\\View\\Composers\\FaqPartialComposer::compose<\/a>"],[0,4,"<a href=\"View\/Composers\/SearchBucketComposer.php.html#20\">App\\View\\Composers\\SearchBucketComposer::compose<\/a>"],[0,1,"<a href=\"View\/Composers\/TestimonialPartialComposer.php.html#20\">App\\View\\Composers\\TestimonialPartialComposer::compose<\/a>"],[0,3,"<a href=\"Services\/ImageHandling\/ImageableTrait.php.html#24\">App\\Services\\ImageHandling\\ImageableTrait::deleteImage<\/a>"],[0,1,"<a href=\"Services\/ImageHandling\/ImageableTrait.php.html#43\">App\\Services\\ImageHandling\\ImageableTrait::storeImage<\/a>"],[0,1,"<a href=\"Services\/PhotoHandling\/PhotoableTrait.php.html#15\">App\\Services\\PhotoHandling\\PhotoableTrait::storePhoto<\/a>"],[0,3,"<a href=\"Traits\/AccessorsMutators\/CustomerAccessorsMutators.php.html#13\">App\\Traits\\AccessorsMutators\\CustomerAccessorsMutators::getCountry<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/FeedbackAccessorsMutators.php.html#13\">App\\Traits\\AccessorsMutators\\FeedbackAccessorsMutators::getAverage<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#8\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getFullNameAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#13\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getFullNameSEOAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#24\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getFullNameSEOCRAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#33\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getGroupCarsTitle<\/a>"],[0,3,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#46\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getGroupImage<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/GroupAccessorsMutators.php.html#57\">App\\Traits\\AccessorsMutators\\GroupAccessorsMutators::getOfferTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ImageAccessorsMutators.php.html#15\">App\\Traits\\AccessorsMutators\\ImageAccessorsMutators::getPath<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/ImageAccessorsMutators.php.html#24\">App\\Traits\\AccessorsMutators\\ImageAccessorsMutators::getListingPath<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/ImageAccessorsMutators.php.html#33\">App\\Traits\\AccessorsMutators\\ImageAccessorsMutators::getLocationPath<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#18\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getOffer<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#30\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#40\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getLongTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#59\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getShortTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#69\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getSEOTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#79\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getSEOShortTitleAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/ListingAccessorsMutators.php.html#85\">App\\Traits\\AccessorsMutators\\ListingAccessorsMutators::getImageAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/LocationAccessorsMutators.php.html#13\">App\\Traits\\AccessorsMutators\\LocationAccessorsMutators::isAirport<\/a>"],[0,3,"<a href=\"Traits\/AccessorsMutators\/LocationAccessorsMutators.php.html#33\">App\\Traits\\AccessorsMutators\\LocationAccessorsMutators::getGroupedForDropdown<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/OfferAccessorsMutators.php.html#12\">App\\Traits\\AccessorsMutators\\OfferAccessorsMutators::getCompactDetails<\/a>"],[0,7,"<a href=\"Traits\/AccessorsMutators\/PhotoAccessorsMutators.php.html#11\">App\\Traits\\AccessorsMutators\\PhotoAccessorsMutators::getSeoAttributes<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#17\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getPickupLocation<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#27\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getPickupLocationDescription<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#37\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getPickupLocationImages<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#47\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getDropoffLocation<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#57\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getDropoffLocationDescription<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/PickupDropoffLocationAccessorsMutators.php.html#67\">App\\Traits\\AccessorsMutators\\PickupDropoffLocationAccessorsMutators::getDropoffLocationImages<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#16\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::getPickupLocation<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#27\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::getDropoffLocation<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#37\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::getDates<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#51\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::setPickupDateAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#61\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::setDropoffDateAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/QuoteAccessorsMutators.php.html#72\">App\\Traits\\AccessorsMutators\\QuoteAccessorsMutators::getTitleAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#18\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::getListingTitle<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#28\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::getPickupLocation<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#38\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::getDropoffLocation<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#48\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::getDates<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#62\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::setPickupDateAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#77\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::setDropoffDateAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#92\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::setDropoffLocationAttribute<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#104\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::hasCustomer<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/RepeatingClientAccessorsMutators.php.html#118\">App\\Traits\\AccessorsMutators\\RepeatingClientAccessorsMutators::getCustomer<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#19\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::getGroup<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#29\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::getDates<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#43\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::setPickupDateAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#53\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::setDropoffDateAttribute<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#62\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::getAccessories<\/a>"],[0,1,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#73\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::getCompactDetails<\/a>"],[0,2,"<a href=\"Traits\/AccessorsMutators\/ReservationAccessorsMutators.php.html#87\">App\\Traits\\AccessorsMutators\\ReservationAccessorsMutators::getTitleAttribute<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/CategoryRelationships.php.html#12\">App\\Traits\\Relationships\\CategoryRelationships::publishedListings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/CustomerRelationships.php.html#11\">App\\Traits\\Relationships\\CustomerRelationships::reservations<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/CustomerRelationships.php.html#19\">App\\Traits\\Relationships\\CustomerRelationships::quotes<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/FeedbackRelationships.php.html#12\">App\\Traits\\Relationships\\FeedbackRelationships::reservation<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GoogleReviewRelationships.php.html#14\">App\\Traits\\Relationships\\GoogleReviewRelationships::reservation<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GoogleReviewRelationships.php.html#22\">App\\Traits\\Relationships\\GoogleReviewRelationships::location<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GoogleReviewRelationships.php.html#30\">App\\Traits\\Relationships\\GoogleReviewRelationships::language<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GroupRelationships.php.html#11\">App\\Traits\\Relationships\\GroupRelationships::listings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GroupRelationships.php.html#16\">App\\Traits\\Relationships\\GroupRelationships::relatedGroups<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GroupRelationships.php.html#26\">App\\Traits\\Relationships\\GroupRelationships::periods<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/GroupRelationships.php.html#34\">App\\Traits\\Relationships\\GroupRelationships::supergroup<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ImageRelationships.php.html#8\">App\\Traits\\Relationships\\ImageRelationships::listings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ImageRelationships.php.html#13\">App\\Traits\\Relationships\\ImageRelationships::locations<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LanguageRelationships.php.html#13\">App\\Traits\\Relationships\\LanguageRelationships::googleReviews<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#15\">App\\Traits\\Relationships\\ListingRelationships::group<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#20\">App\\Traits\\Relationships\\ListingRelationships::categories<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#25\">App\\Traits\\Relationships\\ListingRelationships::getCategoryList<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#30\">App\\Traits\\Relationships\\ListingRelationships::images<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#43\">App\\Traits\\Relationships\\ListingRelationships::related<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#60\">App\\Traits\\Relationships\\ListingRelationships::suggested<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#79\">App\\Traits\\Relationships\\ListingRelationships::similar<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#87\">App\\Traits\\Relationships\\ListingRelationships::motifs<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#98\">App\\Traits\\Relationships\\ListingRelationships::posts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ListingRelationships.php.html#111\">App\\Traits\\Relationships\\ListingRelationships::presentablePosts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LocalisedMotifRelationships.php.html#14\">App\\Traits\\Relationships\\LocalisedMotifRelationships::posts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LocalisedMotifRelationships.php.html#22\">App\\Traits\\Relationships\\LocalisedMotifRelationships::publishedPosts<\/a>"],[0,2,"<a href=\"Traits\/Relationships\/LocationRelationships.php.html#10\">App\\Traits\\Relationships\\LocationRelationships::getImageAttribute<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LocationRelationships.php.html#18\">App\\Traits\\Relationships\\LocationRelationships::images<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/LocationRelationships.php.html#26\">App\\Traits\\Relationships\\LocationRelationships::googleReviews<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/MotifRelationships.php.html#16\">App\\Traits\\Relationships\\MotifRelationships::posts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/MotifRelationships.php.html#24\">App\\Traits\\Relationships\\MotifRelationships::publishedPosts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/MotifRelationships.php.html#34\">App\\Traits\\Relationships\\MotifRelationships::listings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/MotifRelationships.php.html#43\">App\\Traits\\Relationships\\MotifRelationships::publishedListings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#21\">App\\Traits\\Relationships\\OfferRelationships::getCustomer<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#32\">App\\Traits\\Relationships\\OfferRelationships::customer<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#43\">App\\Traits\\Relationships\\OfferRelationships::listing<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#52\">App\\Traits\\Relationships\\OfferRelationships::pickup_loc<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#61\">App\\Traits\\Relationships\\OfferRelationships::dropoff_loc<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#70\">App\\Traits\\Relationships\\OfferRelationships::accessories<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/OfferRelationships.php.html#79\">App\\Traits\\Relationships\\OfferRelationships::reservations<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PeriodRelationships.php.html#16\">App\\Traits\\Relationships\\PeriodRelationships::groups<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PeriodRelationships.php.html#24\">App\\Traits\\Relationships\\PeriodRelationships::changes<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PeriodRelationships.php.html#32\">App\\Traits\\Relationships\\PeriodRelationships::changesGroup<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PeriodRelationships.php.html#41\">App\\Traits\\Relationships\\PeriodRelationships::changesGroupYear<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#18\">App\\Traits\\Relationships\\PostRelationships::tags<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#28\">App\\Traits\\Relationships\\PostRelationships::motifs<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#37\">App\\Traits\\Relationships\\PostRelationships::localisedMotifs<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#43\">App\\Traits\\Relationships\\PostRelationships::getMotifedPosts<\/a>"],[0,9,"<a href=\"Traits\/Relationships\/PostRelationships.php.html#72\">App\\Traits\\Relationships\\PostRelationships::getRelatedPosts<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostTranslationRelationships.php.html#13\">App\\Traits\\Relationships\\PostTranslationRelationships::post<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/PostTranslationRelationships.php.html#21\">App\\Traits\\Relationships\\PostTranslationRelationships::publishedPost<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/QuoteRelationships.php.html#12\">App\\Traits\\Relationships\\QuoteRelationships::getCustomer<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ReservationRelationships.php.html#16\">App\\Traits\\Relationships\\ReservationRelationships::getCustomer<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ReservationRelationships.php.html#25\">App\\Traits\\Relationships\\ReservationRelationships::feedback<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ReservationRelationships.php.html#34\">App\\Traits\\Relationships\\ReservationRelationships::googleReview<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/ReservationRelationships.php.html#43\">App\\Traits\\Relationships\\ReservationRelationships::offer<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/SupergroupRelationships.php.html#15\">App\\Traits\\Relationships\\SupergroupRelationships::groups<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/SupergroupRelationships.php.html#23\">App\\Traits\\Relationships\\SupergroupRelationships::listings<\/a>"],[0,1,"<a href=\"Traits\/Relationships\/TagRelationships.php.html#13\">App\\Traits\\Relationships\\TagRelationships::posts<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/GoogleReviewScopes.php.html#13\">App\\Traits\\Scopes\\GoogleReviewScopes::scopePublished<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/GoogleReviewScopes.php.html#24\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeTestimonialised<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/GoogleReviewScopes.php.html#35\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeShowable<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/GoogleReviewScopes.php.html#53\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeTestimoniable<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/ListingScopes.php.html#13\">App\\Traits\\Scopes\\ListingScopes::scopePublishedEurodollar<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/ListingScopes.php.html#24\">App\\Traits\\Scopes\\ListingScopes::scopePublishedCretanrentals<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#14\">App\\Traits\\Scopes\\LocalisableScopes::scopeEl<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#25\">App\\Traits\\Scopes\\LocalisableScopes::scopeEn<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#36\">App\\Traits\\Scopes\\LocalisableScopes::scopeDe<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#47\">App\\Traits\\Scopes\\LocalisableScopes::scopeFr<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/LocalisableScopes.php.html#58\">App\\Traits\\Scopes\\LocalisableScopes::scopeIt<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/PostScopes.php.html#13\">App\\Traits\\Scopes\\PostScopes::scopePublished<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/PostScopes.php.html#24\">App\\Traits\\Scopes\\PostScopes::scopeFeatured<\/a>"],[0,1,"<a href=\"Traits\/Scopes\/ReservationScopes.php.html#13\">App\\Traits\\Scopes\\ReservationScopes::scopeOnlyShow<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

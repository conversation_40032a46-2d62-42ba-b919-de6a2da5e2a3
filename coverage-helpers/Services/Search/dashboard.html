<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Services/Search</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Search</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ContactLeadSearch.php.html#8">App\Services\Search\ContactLeadSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomerSearch.php.html#8">App\Services\Search\CustomerSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/AuthorShownName.php.html#7">App\Services\Search\Filters\AuthorShownName</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/AuthorTitle.php.html#7">App\Services\Search\Filters\AuthorTitle</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/CreatedAfter.php.html#7">App\Services\Search\Filters\CreatedAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/CreatedBefore.php.html#7">App\Services\Search\Filters\CreatedBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Email.php.html#7">App\Services\Search\Filters\Email</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Enabled.php.html#7">App\Services\Search\Filters\Enabled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Featured.php.html#7">App\Services\Search\Filters\Featured</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#7">App\Services\Search\Filters\GlobalFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GroupId.php.html#7">App\Services\Search\Filters\GroupId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasCategories.php.html#7">App\Services\Search\Filters\HasCategories</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasLocation.php.html#7">App\Services\Search\Filters\HasLocation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasMotifs.php.html#7">App\Services\Search\Filters\HasMotifs</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasReservation.php.html#7">App\Services\Search\Filters\HasReservation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasTags.php.html#7">App\Services\Search\Filters\HasTags</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Id.php.html#7">App\Services\Search\Filters\Id</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Intercepted.php.html#7">App\Services\Search\Filters\Intercepted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Language.php.html#7">App\Services\Search\Filters\Language</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/LocationId.php.html#7">App\Services\Search\Filters\LocationId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Manufacturer.php.html#7">App\Services\Search\Filters\Manufacturer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Model.php.html#7">App\Services\Search\Filters\Model</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/MotifRelatedId.php.html#7">App\Services\Search\Filters\MotifRelatedId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Name.php.html#7">App\Services\Search\Filters\Name</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/OfferId.php.html#7">App\Services\Search\Filters\OfferId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Origin.php.html#7">App\Services\Search\Filters\Origin</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PickupAfter.php.html#7">App\Services\Search\Filters\PickupAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PickupBefore.php.html#7">App\Services\Search\Filters\PickupBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Published.php.html#7">App\Services\Search\Filters\Published</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PublishedCretanrentals.php.html#7">App\Services\Search\Filters\PublishedCretanrentals</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PublishedEurodollar.php.html#7">App\Services\Search\Filters\PublishedEurodollar</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReservationId.php.html#7">App\Services\Search\Filters\ReservationId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewId.php.html#7">App\Services\Search\Filters\ReviewId</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewRating.php.html#7">App\Services\Search\Filters\ReviewRating</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewText.php.html#7">App\Services\Search\Filters\ReviewText</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Sent.php.html#7">App\Services\Search\Filters\Sent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Show.php.html#7">App\Services\Search\Filters\Show</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ShowVerified.php.html#7">App\Services\Search\Filters\ShowVerified</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Site.php.html#7">App\Services\Search\Filters\Site</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Testimonialised.php.html#7">App\Services\Search\Filters\Testimonialised</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Title.php.html#7">App\Services\Search\Filters\Title</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Translated.php.html#7">App\Services\Search\Filters\Translated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TranslationTitle.php.html#7">App\Services\Search\Filters\TranslationTitle</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UpdatedAfter.php.html#7">App\Services\Search\Filters\UpdatedAfter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UpdatedBefore.php.html#7">App\Services\Search\Filters\UpdatedBefore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewSearch.php.html#8">App\Services\Search\GoogleReviewSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingSearch.php.html#8">App\Services\Search\ListingSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferSearch.php.html#8">App\Services\Search\OfferSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostSearch.php.html#8">App\Services\Search\PostSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationSearch.php.html#8">App\Services\Search\ReservationSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Search.php.html#9">App\Services\Search\Search</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Filters/GlobalFilter.php.html#7">App\Services\Search\Filters\GlobalFilter</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Search.php.html#9">App\Services\Search\Search</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ContactLeadSearch.php.html#11"><abbr title="App\Services\Search\ContactLeadSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomerSearch.php.html#11"><abbr title="App\Services\Search\CustomerSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/AuthorShownName.php.html#23"><abbr title="App\Services\Search\Filters\AuthorShownName::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/AuthorTitle.php.html#23"><abbr title="App\Services\Search\Filters\AuthorTitle::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/CreatedAfter.php.html#23"><abbr title="App\Services\Search\Filters\CreatedAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/CreatedBefore.php.html#23"><abbr title="App\Services\Search\Filters\CreatedBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Email.php.html#23"><abbr title="App\Services\Search\Filters\Email::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Enabled.php.html#23"><abbr title="App\Services\Search\Filters\Enabled::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Featured.php.html#23"><abbr title="App\Services\Search\Filters\Featured::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#16"><abbr title="App\Services\Search\Filters\GlobalFilter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#28"><abbr title="App\Services\Search\Filters\GlobalFilter::filterEqual">filterEqual</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#40"><abbr title="App\Services\Search\Filters\GlobalFilter::filterLike">filterLike</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#52"><abbr title="App\Services\Search\Filters\GlobalFilter::filterTranslationLike">filterTranslationLike</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#64"><abbr title="App\Services\Search\Filters\GlobalFilter::filterGTE">filterGTE</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#76"><abbr title="App\Services\Search\Filters\GlobalFilter::filterLTE">filterLTE</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#88"><abbr title="App\Services\Search\Filters\GlobalFilter::filterYN">filterYN</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#108"><abbr title="App\Services\Search\Filters\GlobalFilter::filterRelatedId">filterRelatedId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#122"><abbr title="App\Services\Search\Filters\GlobalFilter::filterBoolean">filterBoolean</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#143"><abbr title="App\Services\Search\Filters\GlobalFilter::filterPseudoBoolean">filterPseudoBoolean</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#163"><abbr title="App\Services\Search\Filters\GlobalFilter::filterTranslatedIn">filterTranslatedIn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/GroupId.php.html#23"><abbr title="App\Services\Search\Filters\GroupId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasCategories.php.html#23"><abbr title="App\Services\Search\Filters\HasCategories::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasLocation.php.html#23"><abbr title="App\Services\Search\Filters\HasLocation::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasMotifs.php.html#23"><abbr title="App\Services\Search\Filters\HasMotifs::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasReservation.php.html#23"><abbr title="App\Services\Search\Filters\HasReservation::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/HasTags.php.html#23"><abbr title="App\Services\Search\Filters\HasTags::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Id.php.html#23"><abbr title="App\Services\Search\Filters\Id::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Intercepted.php.html#23"><abbr title="App\Services\Search\Filters\Intercepted::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Language.php.html#23"><abbr title="App\Services\Search\Filters\Language::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/LocationId.php.html#23"><abbr title="App\Services\Search\Filters\LocationId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Manufacturer.php.html#23"><abbr title="App\Services\Search\Filters\Manufacturer::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Model.php.html#23"><abbr title="App\Services\Search\Filters\Model::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/MotifRelatedId.php.html#23"><abbr title="App\Services\Search\Filters\MotifRelatedId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Name.php.html#23"><abbr title="App\Services\Search\Filters\Name::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/OfferId.php.html#23"><abbr title="App\Services\Search\Filters\OfferId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Origin.php.html#23"><abbr title="App\Services\Search\Filters\Origin::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PickupAfter.php.html#23"><abbr title="App\Services\Search\Filters\PickupAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PickupBefore.php.html#23"><abbr title="App\Services\Search\Filters\PickupBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Published.php.html#23"><abbr title="App\Services\Search\Filters\Published::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PublishedCretanrentals.php.html#23"><abbr title="App\Services\Search\Filters\PublishedCretanrentals::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/PublishedEurodollar.php.html#23"><abbr title="App\Services\Search\Filters\PublishedEurodollar::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReservationId.php.html#23"><abbr title="App\Services\Search\Filters\ReservationId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewId.php.html#23"><abbr title="App\Services\Search\Filters\ReviewId::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewRating.php.html#23"><abbr title="App\Services\Search\Filters\ReviewRating::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ReviewText.php.html#23"><abbr title="App\Services\Search\Filters\ReviewText::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Sent.php.html#23"><abbr title="App\Services\Search\Filters\Sent::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Show.php.html#23"><abbr title="App\Services\Search\Filters\Show::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ShowVerified.php.html#23"><abbr title="App\Services\Search\Filters\ShowVerified::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Site.php.html#23"><abbr title="App\Services\Search\Filters\Site::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Testimonialised.php.html#23"><abbr title="App\Services\Search\Filters\Testimonialised::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Title.php.html#23"><abbr title="App\Services\Search\Filters\Title::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/Translated.php.html#23"><abbr title="App\Services\Search\Filters\Translated::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TranslationTitle.php.html#23"><abbr title="App\Services\Search\Filters\TranslationTitle::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UpdatedAfter.php.html#23"><abbr title="App\Services\Search\Filters\UpdatedAfter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UpdatedBefore.php.html#23"><abbr title="App\Services\Search\Filters\UpdatedBefore::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewSearch.php.html#11"><abbr title="App\Services\Search\GoogleReviewSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingSearch.php.html#11"><abbr title="App\Services\Search\ListingSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferSearch.php.html#11"><abbr title="App\Services\Search\OfferSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostSearch.php.html#11"><abbr title="App\Services\Search\PostSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationSearch.php.html#11"><abbr title="App\Services\Search\ReservationSearch::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Search.php.html#14"><abbr title="App\Services\Search\Search::applyDecoratorsFromFilters">applyDecoratorsFromFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Search.php.html#28"><abbr title="App\Services\Search\Search::createFilterDecorator">createFilterDecorator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Search.php.html#33"><abbr title="App\Services\Search\Search::isValidDecorator">isValidDecorator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Search.php.html#38"><abbr title="App\Services\Search\Search::getResults">getResults</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Filters/GlobalFilter.php.html#88"><abbr title="App\Services\Search\Filters\GlobalFilter::filterYN">filterYN</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#122"><abbr title="App\Services\Search\Filters\GlobalFilter::filterBoolean">filterBoolean</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Filters/GlobalFilter.php.html#143"><abbr title="App\Services\Search\Filters\GlobalFilter::filterPseudoBoolean">filterPseudoBoolean</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Search.php.html#14"><abbr title="App\Services\Search\Search::applyDecoratorsFromFilters">applyDecoratorsFromFilters</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 12:51:21 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([51,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([64,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ContactLeadSearch.php.html#8\">App\\Services\\Search\\ContactLeadSearch<\/a>"],[0,1,"<a href=\"CustomerSearch.php.html#8\">App\\Services\\Search\\CustomerSearch<\/a>"],[0,1,"<a href=\"Filters\/AuthorShownName.php.html#7\">App\\Services\\Search\\Filters\\AuthorShownName<\/a>"],[0,1,"<a href=\"Filters\/AuthorTitle.php.html#7\">App\\Services\\Search\\Filters\\AuthorTitle<\/a>"],[0,1,"<a href=\"Filters\/CreatedAfter.php.html#7\">App\\Services\\Search\\Filters\\CreatedAfter<\/a>"],[0,1,"<a href=\"Filters\/CreatedBefore.php.html#7\">App\\Services\\Search\\Filters\\CreatedBefore<\/a>"],[0,1,"<a href=\"Filters\/Email.php.html#7\">App\\Services\\Search\\Filters\\Email<\/a>"],[0,1,"<a href=\"Filters\/Enabled.php.html#7\">App\\Services\\Search\\Filters\\Enabled<\/a>"],[0,1,"<a href=\"Filters\/Featured.php.html#7\">App\\Services\\Search\\Filters\\Featured<\/a>"],[0,17,"<a href=\"Filters\/GlobalFilter.php.html#7\">App\\Services\\Search\\Filters\\GlobalFilter<\/a>"],[0,1,"<a href=\"Filters\/GroupId.php.html#7\">App\\Services\\Search\\Filters\\GroupId<\/a>"],[0,1,"<a href=\"Filters\/HasCategories.php.html#7\">App\\Services\\Search\\Filters\\HasCategories<\/a>"],[0,1,"<a href=\"Filters\/HasLocation.php.html#7\">App\\Services\\Search\\Filters\\HasLocation<\/a>"],[0,1,"<a href=\"Filters\/HasMotifs.php.html#7\">App\\Services\\Search\\Filters\\HasMotifs<\/a>"],[0,1,"<a href=\"Filters\/HasReservation.php.html#7\">App\\Services\\Search\\Filters\\HasReservation<\/a>"],[0,1,"<a href=\"Filters\/HasTags.php.html#7\">App\\Services\\Search\\Filters\\HasTags<\/a>"],[0,1,"<a href=\"Filters\/Id.php.html#7\">App\\Services\\Search\\Filters\\Id<\/a>"],[0,1,"<a href=\"Filters\/Intercepted.php.html#7\">App\\Services\\Search\\Filters\\Intercepted<\/a>"],[0,1,"<a href=\"Filters\/Language.php.html#7\">App\\Services\\Search\\Filters\\Language<\/a>"],[0,1,"<a href=\"Filters\/LocationId.php.html#7\">App\\Services\\Search\\Filters\\LocationId<\/a>"],[0,1,"<a href=\"Filters\/Manufacturer.php.html#7\">App\\Services\\Search\\Filters\\Manufacturer<\/a>"],[0,1,"<a href=\"Filters\/Model.php.html#7\">App\\Services\\Search\\Filters\\Model<\/a>"],[0,1,"<a href=\"Filters\/MotifRelatedId.php.html#7\">App\\Services\\Search\\Filters\\MotifRelatedId<\/a>"],[0,1,"<a href=\"Filters\/Name.php.html#7\">App\\Services\\Search\\Filters\\Name<\/a>"],[0,1,"<a href=\"Filters\/OfferId.php.html#7\">App\\Services\\Search\\Filters\\OfferId<\/a>"],[0,1,"<a href=\"Filters\/Origin.php.html#7\">App\\Services\\Search\\Filters\\Origin<\/a>"],[0,1,"<a href=\"Filters\/PickupAfter.php.html#7\">App\\Services\\Search\\Filters\\PickupAfter<\/a>"],[0,1,"<a href=\"Filters\/PickupBefore.php.html#7\">App\\Services\\Search\\Filters\\PickupBefore<\/a>"],[0,1,"<a href=\"Filters\/Published.php.html#7\">App\\Services\\Search\\Filters\\Published<\/a>"],[0,1,"<a href=\"Filters\/PublishedCretanrentals.php.html#7\">App\\Services\\Search\\Filters\\PublishedCretanrentals<\/a>"],[0,1,"<a href=\"Filters\/PublishedEurodollar.php.html#7\">App\\Services\\Search\\Filters\\PublishedEurodollar<\/a>"],[0,1,"<a href=\"Filters\/ReservationId.php.html#7\">App\\Services\\Search\\Filters\\ReservationId<\/a>"],[0,1,"<a href=\"Filters\/ReviewId.php.html#7\">App\\Services\\Search\\Filters\\ReviewId<\/a>"],[0,1,"<a href=\"Filters\/ReviewRating.php.html#7\">App\\Services\\Search\\Filters\\ReviewRating<\/a>"],[0,1,"<a href=\"Filters\/ReviewText.php.html#7\">App\\Services\\Search\\Filters\\ReviewText<\/a>"],[0,1,"<a href=\"Filters\/Sent.php.html#7\">App\\Services\\Search\\Filters\\Sent<\/a>"],[0,1,"<a href=\"Filters\/Show.php.html#7\">App\\Services\\Search\\Filters\\Show<\/a>"],[0,1,"<a href=\"Filters\/ShowVerified.php.html#7\">App\\Services\\Search\\Filters\\ShowVerified<\/a>"],[0,1,"<a href=\"Filters\/Site.php.html#7\">App\\Services\\Search\\Filters\\Site<\/a>"],[0,1,"<a href=\"Filters\/Testimonialised.php.html#7\">App\\Services\\Search\\Filters\\Testimonialised<\/a>"],[0,1,"<a href=\"Filters\/Title.php.html#7\">App\\Services\\Search\\Filters\\Title<\/a>"],[0,1,"<a href=\"Filters\/Translated.php.html#7\">App\\Services\\Search\\Filters\\Translated<\/a>"],[0,1,"<a href=\"Filters\/TranslationTitle.php.html#7\">App\\Services\\Search\\Filters\\TranslationTitle<\/a>"],[0,1,"<a href=\"Filters\/UpdatedAfter.php.html#7\">App\\Services\\Search\\Filters\\UpdatedAfter<\/a>"],[0,1,"<a href=\"Filters\/UpdatedBefore.php.html#7\">App\\Services\\Search\\Filters\\UpdatedBefore<\/a>"],[0,1,"<a href=\"GoogleReviewSearch.php.html#8\">App\\Services\\Search\\GoogleReviewSearch<\/a>"],[0,1,"<a href=\"ListingSearch.php.html#8\">App\\Services\\Search\\ListingSearch<\/a>"],[0,1,"<a href=\"OfferSearch.php.html#8\">App\\Services\\Search\\OfferSearch<\/a>"],[0,1,"<a href=\"PostSearch.php.html#8\">App\\Services\\Search\\PostSearch<\/a>"],[0,1,"<a href=\"ReservationSearch.php.html#8\">App\\Services\\Search\\ReservationSearch<\/a>"],[0,6,"<a href=\"Search.php.html#9\">App\\Services\\Search\\Search<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ContactLeadSearch.php.html#11\">App\\Services\\Search\\ContactLeadSearch::apply<\/a>"],[0,1,"<a href=\"CustomerSearch.php.html#11\">App\\Services\\Search\\CustomerSearch::apply<\/a>"],[0,1,"<a href=\"Filters\/AuthorShownName.php.html#23\">App\\Services\\Search\\Filters\\AuthorShownName::apply<\/a>"],[0,1,"<a href=\"Filters\/AuthorTitle.php.html#23\">App\\Services\\Search\\Filters\\AuthorTitle::apply<\/a>"],[0,1,"<a href=\"Filters\/CreatedAfter.php.html#23\">App\\Services\\Search\\Filters\\CreatedAfter::apply<\/a>"],[0,1,"<a href=\"Filters\/CreatedBefore.php.html#23\">App\\Services\\Search\\Filters\\CreatedBefore::apply<\/a>"],[0,1,"<a href=\"Filters\/Email.php.html#23\">App\\Services\\Search\\Filters\\Email::apply<\/a>"],[0,1,"<a href=\"Filters\/Enabled.php.html#23\">App\\Services\\Search\\Filters\\Enabled::apply<\/a>"],[0,1,"<a href=\"Filters\/Featured.php.html#23\">App\\Services\\Search\\Filters\\Featured::apply<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#16\">App\\Services\\Search\\Filters\\GlobalFilter::apply<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#28\">App\\Services\\Search\\Filters\\GlobalFilter::filterEqual<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#40\">App\\Services\\Search\\Filters\\GlobalFilter::filterLike<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#52\">App\\Services\\Search\\Filters\\GlobalFilter::filterTranslationLike<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#64\">App\\Services\\Search\\Filters\\GlobalFilter::filterGTE<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#76\">App\\Services\\Search\\Filters\\GlobalFilter::filterLTE<\/a>"],[0,3,"<a href=\"Filters\/GlobalFilter.php.html#88\">App\\Services\\Search\\Filters\\GlobalFilter::filterYN<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#108\">App\\Services\\Search\\Filters\\GlobalFilter::filterRelatedId<\/a>"],[0,3,"<a href=\"Filters\/GlobalFilter.php.html#122\">App\\Services\\Search\\Filters\\GlobalFilter::filterBoolean<\/a>"],[0,3,"<a href=\"Filters\/GlobalFilter.php.html#143\">App\\Services\\Search\\Filters\\GlobalFilter::filterPseudoBoolean<\/a>"],[0,1,"<a href=\"Filters\/GlobalFilter.php.html#163\">App\\Services\\Search\\Filters\\GlobalFilter::filterTranslatedIn<\/a>"],[0,1,"<a href=\"Filters\/GroupId.php.html#23\">App\\Services\\Search\\Filters\\GroupId::apply<\/a>"],[0,1,"<a href=\"Filters\/HasCategories.php.html#23\">App\\Services\\Search\\Filters\\HasCategories::apply<\/a>"],[0,1,"<a href=\"Filters\/HasLocation.php.html#23\">App\\Services\\Search\\Filters\\HasLocation::apply<\/a>"],[0,1,"<a href=\"Filters\/HasMotifs.php.html#23\">App\\Services\\Search\\Filters\\HasMotifs::apply<\/a>"],[0,1,"<a href=\"Filters\/HasReservation.php.html#23\">App\\Services\\Search\\Filters\\HasReservation::apply<\/a>"],[0,1,"<a href=\"Filters\/HasTags.php.html#23\">App\\Services\\Search\\Filters\\HasTags::apply<\/a>"],[0,1,"<a href=\"Filters\/Id.php.html#23\">App\\Services\\Search\\Filters\\Id::apply<\/a>"],[0,1,"<a href=\"Filters\/Intercepted.php.html#23\">App\\Services\\Search\\Filters\\Intercepted::apply<\/a>"],[0,1,"<a href=\"Filters\/Language.php.html#23\">App\\Services\\Search\\Filters\\Language::apply<\/a>"],[0,1,"<a href=\"Filters\/LocationId.php.html#23\">App\\Services\\Search\\Filters\\LocationId::apply<\/a>"],[0,1,"<a href=\"Filters\/Manufacturer.php.html#23\">App\\Services\\Search\\Filters\\Manufacturer::apply<\/a>"],[0,1,"<a href=\"Filters\/Model.php.html#23\">App\\Services\\Search\\Filters\\Model::apply<\/a>"],[0,1,"<a href=\"Filters\/MotifRelatedId.php.html#23\">App\\Services\\Search\\Filters\\MotifRelatedId::apply<\/a>"],[0,1,"<a href=\"Filters\/Name.php.html#23\">App\\Services\\Search\\Filters\\Name::apply<\/a>"],[0,1,"<a href=\"Filters\/OfferId.php.html#23\">App\\Services\\Search\\Filters\\OfferId::apply<\/a>"],[0,1,"<a href=\"Filters\/Origin.php.html#23\">App\\Services\\Search\\Filters\\Origin::apply<\/a>"],[0,1,"<a href=\"Filters\/PickupAfter.php.html#23\">App\\Services\\Search\\Filters\\PickupAfter::apply<\/a>"],[0,1,"<a href=\"Filters\/PickupBefore.php.html#23\">App\\Services\\Search\\Filters\\PickupBefore::apply<\/a>"],[0,1,"<a href=\"Filters\/Published.php.html#23\">App\\Services\\Search\\Filters\\Published::apply<\/a>"],[0,1,"<a href=\"Filters\/PublishedCretanrentals.php.html#23\">App\\Services\\Search\\Filters\\PublishedCretanrentals::apply<\/a>"],[0,1,"<a href=\"Filters\/PublishedEurodollar.php.html#23\">App\\Services\\Search\\Filters\\PublishedEurodollar::apply<\/a>"],[0,1,"<a href=\"Filters\/ReservationId.php.html#23\">App\\Services\\Search\\Filters\\ReservationId::apply<\/a>"],[0,1,"<a href=\"Filters\/ReviewId.php.html#23\">App\\Services\\Search\\Filters\\ReviewId::apply<\/a>"],[0,1,"<a href=\"Filters\/ReviewRating.php.html#23\">App\\Services\\Search\\Filters\\ReviewRating::apply<\/a>"],[0,1,"<a href=\"Filters\/ReviewText.php.html#23\">App\\Services\\Search\\Filters\\ReviewText::apply<\/a>"],[0,1,"<a href=\"Filters\/Sent.php.html#23\">App\\Services\\Search\\Filters\\Sent::apply<\/a>"],[0,1,"<a href=\"Filters\/Show.php.html#23\">App\\Services\\Search\\Filters\\Show::apply<\/a>"],[0,1,"<a href=\"Filters\/ShowVerified.php.html#23\">App\\Services\\Search\\Filters\\ShowVerified::apply<\/a>"],[0,1,"<a href=\"Filters\/Site.php.html#23\">App\\Services\\Search\\Filters\\Site::apply<\/a>"],[0,1,"<a href=\"Filters\/Testimonialised.php.html#23\">App\\Services\\Search\\Filters\\Testimonialised::apply<\/a>"],[0,1,"<a href=\"Filters\/Title.php.html#23\">App\\Services\\Search\\Filters\\Title::apply<\/a>"],[0,1,"<a href=\"Filters\/Translated.php.html#23\">App\\Services\\Search\\Filters\\Translated::apply<\/a>"],[0,1,"<a href=\"Filters\/TranslationTitle.php.html#23\">App\\Services\\Search\\Filters\\TranslationTitle::apply<\/a>"],[0,1,"<a href=\"Filters\/UpdatedAfter.php.html#23\">App\\Services\\Search\\Filters\\UpdatedAfter::apply<\/a>"],[0,1,"<a href=\"Filters\/UpdatedBefore.php.html#23\">App\\Services\\Search\\Filters\\UpdatedBefore::apply<\/a>"],[0,1,"<a href=\"GoogleReviewSearch.php.html#11\">App\\Services\\Search\\GoogleReviewSearch::apply<\/a>"],[0,1,"<a href=\"ListingSearch.php.html#11\">App\\Services\\Search\\ListingSearch::apply<\/a>"],[0,1,"<a href=\"OfferSearch.php.html#11\">App\\Services\\Search\\OfferSearch::apply<\/a>"],[0,1,"<a href=\"PostSearch.php.html#11\">App\\Services\\Search\\PostSearch::apply<\/a>"],[0,1,"<a href=\"ReservationSearch.php.html#11\">App\\Services\\Search\\ReservationSearch::apply<\/a>"],[100,0,"<a href=\"Search.php.html#12\">App\\Services\\Search\\Search::apply<\/a>"],[0,3,"<a href=\"Search.php.html#14\">App\\Services\\Search\\Search::applyDecoratorsFromFilters<\/a>"],[0,1,"<a href=\"Search.php.html#28\">App\\Services\\Search\\Search::createFilterDecorator<\/a>"],[0,1,"<a href=\"Search.php.html#33\">App\\Services\\Search\\Search::isValidDecorator<\/a>"],[0,1,"<a href=\"Search.php.html#38\">App\\Services\\Search\\Search::getResults<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

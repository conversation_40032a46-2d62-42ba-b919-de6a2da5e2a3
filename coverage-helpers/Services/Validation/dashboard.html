<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Services/Validation</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Validation</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessoryValidator.php.html#13">App\Services\Validation\AccessoryValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponValidator.php.html#7">App\Services\Validation\CouponValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationValidator.php.html#13">App\Services\Validation\LocationValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationValidator.php.html#13">App\Services\Validation\ReservationValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationException.php.html#5">App\Services\Validation\ValidationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Validator.php.html#5">App\Services\Validation\Validator</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Validator.php.html#5">App\Services\Validation\Validator</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccessoryValidator.php.html#13">App\Services\Validation\AccessoryValidator</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LocationValidator.php.html#13">App\Services\Validation\LocationValidator</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessoryValidator.php.html#38"><abbr title="App\Services\Validation\AccessoryValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponValidator.php.html#33"><abbr title="App\Services\Validation\CouponValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationValidator.php.html#36"><abbr title="App\Services\Validation\LocationValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationValidator.php.html#22"><abbr title="App\Services\Validation\ReservationValidator::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationException.php.html#22"><abbr title="App\Services\Validation\ValidationException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidationException.php.html#34"><abbr title="App\Services\Validation\ValidationException::getErrors">getErrors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Validator.php.html#16"><abbr title="App\Services\Validation\Validator::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Validator.php.html#33"><abbr title="App\Services\Validation\Validator::validateForCreation">validateForCreation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Validator.php.html#46"><abbr title="App\Services\Validation\Validator::validateForUpdate">validateForUpdate</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessoryValidator.php.html#38"><abbr title="App\Services\Validation\AccessoryValidator::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LocationValidator.php.html#36"><abbr title="App\Services\Validation\LocationValidator::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Validator.php.html#16"><abbr title="App\Services\Validation\Validator::validate">validate</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 12:51:21 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,2], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([9,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"AccessoryValidator.php.html#13\">App\\Services\\Validation\\AccessoryValidator<\/a>"],[0,1,"<a href=\"CouponValidator.php.html#7\">App\\Services\\Validation\\CouponValidator<\/a>"],[0,2,"<a href=\"LocationValidator.php.html#13\">App\\Services\\Validation\\LocationValidator<\/a>"],[100,0,"<a href=\"QuoteValidator.php.html#12\">App\\Services\\Validation\\QuoteValidator<\/a>"],[100,0,"<a href=\"RepeatingClientValidator.php.html#12\">App\\Services\\Validation\\RepeatingClientValidator<\/a>"],[0,1,"<a href=\"ReservationValidator.php.html#13\">App\\Services\\Validation\\ReservationValidator<\/a>"],[0,2,"<a href=\"ValidationException.php.html#5\">App\\Services\\Validation\\ValidationException<\/a>"],[0,4,"<a href=\"Validator.php.html#5\">App\\Services\\Validation\\Validator<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"AccessoryValidator.php.html#38\">App\\Services\\Validation\\AccessoryValidator::__construct<\/a>"],[0,1,"<a href=\"CouponValidator.php.html#33\">App\\Services\\Validation\\CouponValidator::__construct<\/a>"],[0,2,"<a href=\"LocationValidator.php.html#36\">App\\Services\\Validation\\LocationValidator::__construct<\/a>"],[0,1,"<a href=\"ReservationValidator.php.html#22\">App\\Services\\Validation\\ReservationValidator::__construct<\/a>"],[0,1,"<a href=\"ValidationException.php.html#22\">App\\Services\\Validation\\ValidationException::__construct<\/a>"],[0,1,"<a href=\"ValidationException.php.html#34\">App\\Services\\Validation\\ValidationException::getErrors<\/a>"],[0,2,"<a href=\"Validator.php.html#16\">App\\Services\\Validation\\Validator::validate<\/a>"],[0,1,"<a href=\"Validator.php.html#33\">App\\Services\\Validation\\Validator::validateForCreation<\/a>"],[0,1,"<a href=\"Validator.php.html#46\">App\\Services\\Validation\\Validator::validateForUpdate<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

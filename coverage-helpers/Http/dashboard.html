<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Http</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Http</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/AccessoriesController.php.html#10">App\Http\Controllers\AccessoriesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#11">App\Http\Controllers\Auth\LoginController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#9">App\Http\Controllers\Auth\TwoFactorController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#10">App\Http\Controllers\AuthController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#10">App\Http\Controllers\BookingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#10">App\Http\Controllers\CategoriesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#20">App\Http\Controllers\ContactController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#24">App\Http\Controllers\Controller</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#12">App\Http\Controllers\CouponsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CustomersController.php.html#8">App\Http\Controllers\CustomersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/DashboardController.php.html#9">App\Http\Controllers\DashboardController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedController.php.html#8">App\Http\Controllers\FeedController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#13">App\Http\Controllers\FeedbackController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/HomeController.php.html#9">App\Http\Controllers\HomeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#12">App\Http\Controllers\ImageController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#20">App\Http\Controllers\ListingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#10">App\Http\Controllers\LocationsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OffersController.php.html#14">App\Http\Controllers\OffersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#8">App\Http\Controllers\PagesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#12">App\Http\Controllers\PlacesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#11">App\Http\Controllers\PopupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#14">App\Http\Controllers\PostsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#13">App\Http\Controllers\PricingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#18">App\Http\Controllers\QuotesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#15">App\Http\Controllers\RepeatingClientsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#20">App\Http\Controllers\ReservationsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReviewsController.php.html#5">App\Http\Controllers\ReviewsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RobotsController.php.html#4">App\Http\Controllers\RobotsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SearchController.php.html#10">App\Http\Controllers\SearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SessionsController.php.html#7">App\Http\Controllers\SessionsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TestController.php.html#9">App\Http\Controllers\TestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/AvailabilityRequest.php.html#7">App\Http\Requests\AvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#7">App\Http\Requests\ContactRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/PricingRequest.php.html#7">App\Http\Requests\PricingRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#7">App\Http\Requests\ReservationRequest</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/ListingsController.php.html#20">App\Http\Controllers\ListingsController</a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#20">App\Http\Controllers\ReservationsController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#12">App\Http\Controllers\PlacesController</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#10">App\Http\Controllers\BookingsController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#24">App\Http\Controllers\Controller</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#12">App\Http\Controllers\ImageController</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#13">App\Http\Controllers\FeedbackController</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#10">App\Http\Controllers\CategoriesController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#12">App\Http\Controllers\CouponsController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#8">App\Http\Controllers\PagesController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#11">App\Http\Controllers\PopupController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#14">App\Http\Controllers\PostsController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#13">App\Http\Controllers\PricingController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#15">App\Http\Controllers\RepeatingClientsController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#10">App\Http\Controllers\AccessoriesController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#10">App\Http\Controllers\LocationsController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#18">App\Http\Controllers\QuotesController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#7">App\Http\Requests\ReservationRequest</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#20">App\Http\Controllers\ContactController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#7">App\Http\Requests\ContactRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#11">App\Http\Controllers\Auth\LoginController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#9">App\Http\Controllers\Auth\TwoFactorController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#10">App\Http\Controllers\AuthController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/FeedController.php.html#8">App\Http\Controllers\FeedController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/HomeController.php.html#9">App\Http\Controllers\HomeController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/OffersController.php.html#14">App\Http\Controllers\OffersController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/SearchController.php.html#10">App\Http\Controllers\SearchController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/CustomersController.php.html#8">App\Http\Controllers\CustomersController</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/AccessoriesController.php.html#17"><abbr title="App\Http\Controllers\AccessoriesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#32"><abbr title="App\Http\Controllers\AccessoriesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#43"><abbr title="App\Http\Controllers\AccessoriesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#70"><abbr title="App\Http\Controllers\AccessoriesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#87"><abbr title="App\Http\Controllers\AccessoriesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#114"><abbr title="App\Http\Controllers\AccessoriesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#38"><abbr title="App\Http\Controllers\Auth\LoginController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#43"><abbr title="App\Http\Controllers\Auth\LoginController::username">username</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#48"><abbr title="App\Http\Controllers\Auth\LoginController::authenticated">authenticated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#16"><abbr title="App\Http\Controllers\Auth\TwoFactorController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#21"><abbr title="App\Http\Controllers\Auth\TwoFactorController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#26"><abbr title="App\Http\Controllers\Auth\TwoFactorController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#46"><abbr title="App\Http\Controllers\Auth\TwoFactorController::resend">resend</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#15"><abbr title="App\Http\Controllers\AuthController::showLogin">showLogin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#30"><abbr title="App\Http\Controllers\AuthController::handleLogin">handleLogin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#17"><abbr title="App\Http\Controllers\BookingsController::getCurrentReservationData">getCurrentReservationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#34"><abbr title="App\Http\Controllers\BookingsController::showBooking">showBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#57"><abbr title="App\Http\Controllers\BookingsController::handleBooking">handleBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#80"><abbr title="App\Http\Controllers\BookingsController::fleetIndex">fleetIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#172"><abbr title="App\Http\Controllers\BookingsController::initialSetupOfCookie">initialSetupOfCookie</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#17"><abbr title="App\Http\Controllers\CategoriesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#33"><abbr title="App\Http\Controllers\CategoriesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#46"><abbr title="App\Http\Controllers\CategoriesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#71"><abbr title="App\Http\Controllers\CategoriesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#86"><abbr title="App\Http\Controllers\CategoriesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#116"><abbr title="App\Http\Controllers\CategoriesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#28"><abbr title="App\Http\Controllers\ContactController::showContact">showContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#73"><abbr title="App\Http\Controllers\ContactController::handleContact">handleContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#151"><abbr title="App\Http\Controllers\ContactController::handleNewsletter">handleNewsletter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#176"><abbr title="App\Http\Controllers\ContactController::getNewslettersIndex">getNewslettersIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#32"><abbr title="App\Http\Controllers\Controller::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#132"><abbr title="App\Http\Controllers\Controller::setupLayout">setupLayout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#149"><abbr title="App\Http\Controllers\Controller::overwriteOpengraph">overwriteOpengraph</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#165"><abbr title="App\Http\Controllers\Controller::popup">popup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#21"><abbr title="App\Http\Controllers\CouponsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#44"><abbr title="App\Http\Controllers\CouponsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#59"><abbr title="App\Http\Controllers\CouponsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#88"><abbr title="App\Http\Controllers\CouponsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#100"><abbr title="App\Http\Controllers\CouponsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#118"><abbr title="App\Http\Controllers\CouponsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#150"><abbr title="App\Http\Controllers\CouponsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/CustomersController.php.html#14"><abbr title="App\Http\Controllers\CustomersController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/DashboardController.php.html#16"><abbr title="App\Http\Controllers\DashboardController::getIndex">getIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedController.php.html#12"><abbr title="App\Http\Controllers\FeedController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedController.php.html#25"><abbr title="App\Http\Controllers\FeedController::exportCarsCsv">exportCarsCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#20"><abbr title="App\Http\Controllers\FeedbackController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#59"><abbr title="App\Http\Controllers\FeedbackController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#148"><abbr title="App\Http\Controllers\FeedbackController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#164"><abbr title="App\Http\Controllers\FeedbackController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/HomeController.php.html#12"><abbr title="App\Http\Controllers\HomeController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/HomeController.php.html#20"><abbr title="App\Http\Controllers\HomeController::getIndex">getIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#18"><abbr title="App\Http\Controllers\ImageController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#32"><abbr title="App\Http\Controllers\ImageController::postUpload">postUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#40"><abbr title="App\Http\Controllers\ImageController::deleteUpload">deleteUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#55"><abbr title="App\Http\Controllers\ImageController::getListingImages">getListingImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#86"><abbr title="App\Http\Controllers\ImageController::getLocationImages">getLocationImages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#24"><abbr title="App\Http\Controllers\ListingsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#36"><abbr title="App\Http\Controllers\ListingsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#255"><abbr title="App\Http\Controllers\ListingsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#477"><abbr title="App\Http\Controllers\ListingsController::getPrice">getPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#550"><abbr title="App\Http\Controllers\ListingsController::handleUnavailable">handleUnavailable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#18"><abbr title="App\Http\Controllers\LocationsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#34"><abbr title="App\Http\Controllers\LocationsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#47"><abbr title="App\Http\Controllers\LocationsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#77"><abbr title="App\Http\Controllers\LocationsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#93"><abbr title="App\Http\Controllers\LocationsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#123"><abbr title="App\Http\Controllers\LocationsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OffersController.php.html#23"><abbr title="App\Http\Controllers\OffersController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#10"><abbr title="App\Http\Controllers\PagesController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#21"><abbr title="App\Http\Controllers\PagesController::about">about</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#61"><abbr title="App\Http\Controllers\PagesController::offers">offers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#70"><abbr title="App\Http\Controllers\PagesController::crete">crete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#78"><abbr title="App\Http\Controllers\PagesController::services">services</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#118"><abbr title="App\Http\Controllers\PagesController::branches">branches</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#127"><abbr title="App\Http\Controllers\PagesController::faq">faq</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#189"><abbr title="App\Http\Controllers\PagesController::policy">policy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#229"><abbr title="App\Http\Controllers\PagesController::privacy">privacy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#260"><abbr title="App\Http\Controllers\PagesController::safety">safety</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#293"><abbr title="App\Http\Controllers\PagesController::insurance">insurance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#32"><abbr title="App\Http\Controllers\PlacesController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#74"><abbr title="App\Http\Controllers\PlacesController::heraklion">heraklion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#184"><abbr title="App\Http\Controllers\PlacesController::heraklion_airport">heraklion_airport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#298"><abbr title="App\Http\Controllers\PlacesController::chania_airport">chania_airport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#411"><abbr title="App\Http\Controllers\PlacesController::chania">chania</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#522"><abbr title="App\Http\Controllers\PlacesController::rethymno">rethymno</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#634"><abbr title="App\Http\Controllers\PlacesController::agios_nikolaos">agios_nikolaos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#749"><abbr title="App\Http\Controllers\PlacesController::setDefaultLocation">setDefaultLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#18"><abbr title="App\Http\Controllers\PopupController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#33"><abbr title="App\Http\Controllers\PopupController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#48"><abbr title="App\Http\Controllers\PopupController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#72"><abbr title="App\Http\Controllers\PopupController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#90"><abbr title="App\Http\Controllers\PopupController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#114"><abbr title="App\Http\Controllers\PopupController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#130"><abbr title="App\Http\Controllers\PopupController::return_bytes">return_bytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#16"><abbr title="App\Http\Controllers\PostsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#28"><abbr title="App\Http\Controllers\PostsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#37"><abbr title="App\Http\Controllers\PostsController::indexWithTags">indexWithTags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#48"><abbr title="App\Http\Controllers\PostsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#139"><abbr title="App\Http\Controllers\PostsController::handleIndex">handleIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#20"><abbr title="App\Http\Controllers\PricingController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#37"><abbr title="App\Http\Controllers\PricingController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#73"><abbr title="App\Http\Controllers\PricingController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#113"><abbr title="App\Http\Controllers\PricingController::availabilityUpdate">availabilityUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#26"><abbr title="App\Http\Controllers\QuotesController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#47"><abbr title="App\Http\Controllers\QuotesController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#57"><abbr title="App\Http\Controllers\QuotesController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#112"><abbr title="App\Http\Controllers\QuotesController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#132"><abbr title="App\Http\Controllers\QuotesController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#156"><abbr title="App\Http\Controllers\QuotesController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#22"><abbr title="App\Http\Controllers\RepeatingClientsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#44"><abbr title="App\Http\Controllers\RepeatingClientsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#55"><abbr title="App\Http\Controllers\RepeatingClientsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#95"><abbr title="App\Http\Controllers\RepeatingClientsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#114"><abbr title="App\Http\Controllers\RepeatingClientsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#126"><abbr title="App\Http\Controllers\RepeatingClientsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#151"><abbr title="App\Http\Controllers\RepeatingClientsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#27"><abbr title="App\Http\Controllers\ReservationsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#160"><abbr title="App\Http\Controllers\ReservationsController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#171"><abbr title="App\Http\Controllers\ReservationsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#246"><abbr title="App\Http\Controllers\ReservationsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#266"><abbr title="App\Http\Controllers\ReservationsController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#278"><abbr title="App\Http\Controllers\ReservationsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#299"><abbr title="App\Http\Controllers\ReservationsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#319"><abbr title="App\Http\Controllers\ReservationsController::confirm">confirm</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#343"><abbr title="App\Http\Controllers\ReservationsController::verify">verify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReviewsController.php.html#8"><abbr title="App\Http\Controllers\ReviewsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ReviewsController.php.html#16"><abbr title="App\Http\Controllers\ReviewsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RobotsController.php.html#6"><abbr title="App\Http\Controllers\RobotsController::robots">robots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SearchController.php.html#13"><abbr title="App\Http\Controllers\SearchController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SearchController.php.html#21"><abbr title="App\Http\Controllers\SearchController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SessionsController.php.html#14"><abbr title="App\Http\Controllers\SessionsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SessionsController.php.html#28"><abbr title="App\Http\Controllers\SessionsController::destroyAll">destroyAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TestController.php.html#12"><abbr title="App\Http\Controllers\TestController::offerEmail">offerEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TestController.php.html#33"><abbr title="App\Http\Controllers\TestController::deepl">deepl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TestController.php.html#48"><abbr title="App\Http\Controllers\TestController::multiMail">multiMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/AvailabilityRequest.php.html#14"><abbr title="App\Http\Requests\AvailabilityRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/AvailabilityRequest.php.html#24"><abbr title="App\Http\Requests\AvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#14"><abbr title="App\Http\Requests\ContactRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#24"><abbr title="App\Http\Requests\ContactRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#31"><abbr title="App\Http\Requests\ContactRequest::sanitize">sanitize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/PricingRequest.php.html#14"><abbr title="App\Http\Requests\PricingRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/PricingRequest.php.html#24"><abbr title="App\Http\Requests\PricingRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#14"><abbr title="App\Http\Requests\ReservationRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#24"><abbr title="App\Http\Requests\ReservationRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#31"><abbr title="App\Http\Requests\ReservationRequest::sanitize">sanitize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/ReservationsController.php.html#27"><abbr title="App\Http\Controllers\ReservationsController::index">index</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#255"><abbr title="App\Http\Controllers\ListingsController::show">show</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#172"><abbr title="App\Http\Controllers\BookingsController::initialSetupOfCookie">initialSetupOfCookie</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#477"><abbr title="App\Http\Controllers\ListingsController::getPrice">getPrice</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/ListingsController.php.html#36"><abbr title="App\Http\Controllers\ListingsController::index">index</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/BookingsController.php.html#80"><abbr title="App\Http\Controllers\BookingsController::fleetIndex">fleetIndex</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#165"><abbr title="App\Http\Controllers\Controller::popup">popup</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#59"><abbr title="App\Http\Controllers\FeedbackController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/ReservationRequest.php.html#31"><abbr title="App\Http\Requests\ReservationRequest::sanitize">sanitize</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#32"><abbr title="App\Http\Controllers\Controller::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#48"><abbr title="App\Http\Controllers\PostsController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#86"><abbr title="App\Http\Controllers\CategoriesController::update">update</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#73"><abbr title="App\Http\Controllers\ContactController::handleContact">handleContact</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/FeedbackController.php.html#20"><abbr title="App\Http\Controllers\FeedbackController::create">create</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#18"><abbr title="App\Http\Controllers\ImageController::__construct">__construct</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/FeedController.php.html#25"><abbr title="App\Http\Controllers\FeedController::exportCarsCsv">exportCarsCsv</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#55"><abbr title="App\Http\Controllers\ImageController::getListingImages">getListingImages</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#86"><abbr title="App\Http\Controllers\ImageController::getLocationImages">getLocationImages</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#74"><abbr title="App\Http\Controllers\PlacesController::heraklion">heraklion</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#411"><abbr title="App\Http\Controllers\PlacesController::chania">chania</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#522"><abbr title="App\Http\Controllers\PlacesController::rethymno">rethymno</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#634"><abbr title="App\Http\Controllers\PlacesController::agios_nikolaos">agios_nikolaos</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#130"><abbr title="App\Http\Controllers\PopupController::return_bytes">return_bytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#73"><abbr title="App\Http\Controllers\PricingController::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#113"><abbr title="App\Http\Controllers\PricingController::availabilityUpdate">availabilityUpdate</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#343"><abbr title="App\Http\Controllers\ReservationsController::verify">verify</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/ContactRequest.php.html#31"><abbr title="App\Http\Requests\ContactRequest::sanitize">sanitize</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#43"><abbr title="App\Http\Controllers\AccessoriesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/AccessoriesController.php.html#87"><abbr title="App\Http\Controllers\AccessoriesController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Auth/LoginController.php.html#48"><abbr title="App\Http\Controllers\Auth\LoginController::authenticated">authenticated</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#30"><abbr title="App\Http\Controllers\AuthController::handleLogin">handleLogin</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#46"><abbr title="App\Http\Controllers\CategoriesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#59"><abbr title="App\Http\Controllers\CouponsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#118"><abbr title="App\Http\Controllers\CouponsController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#47"><abbr title="App\Http\Controllers\LocationsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/LocationsController.php.html#93"><abbr title="App\Http\Controllers\LocationsController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/OffersController.php.html#23"><abbr title="App\Http\Controllers\OffersController::show">show</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#184"><abbr title="App\Http\Controllers\PlacesController::heraklion_airport">heraklion_airport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#298"><abbr title="App\Http\Controllers\PlacesController::chania_airport">chania_airport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#57"><abbr title="App\Http\Controllers\QuotesController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#55"><abbr title="App\Http\Controllers\RepeatingClientsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#171"><abbr title="App\Http\Controllers\ReservationsController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Auth/TwoFactorController.php.html#26"><abbr title="App\Http\Controllers\Auth\TwoFactorController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/AuthController.php.html#15"><abbr title="App\Http\Controllers\AuthController::showLogin">showLogin</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/CategoriesController.php.html#116"><abbr title="App\Http\Controllers\CategoriesController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ContactController.php.html#151"><abbr title="App\Http\Controllers\ContactController::handleNewsletter">handleNewsletter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#132"><abbr title="App\Http\Controllers\Controller::setupLayout">setupLayout</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Controller.php.html#149"><abbr title="App\Http\Controllers\Controller::overwriteOpengraph">overwriteOpengraph</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/CouponsController.php.html#21"><abbr title="App\Http\Controllers\CouponsController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/CustomersController.php.html#14"><abbr title="App\Http\Controllers\CustomersController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/HomeController.php.html#20"><abbr title="App\Http\Controllers\HomeController::getIndex">getIndex</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ImageController.php.html#40"><abbr title="App\Http\Controllers\ImageController::deleteUpload">deleteUpload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PagesController.php.html#127"><abbr title="App\Http\Controllers\PagesController::faq">faq</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PlacesController.php.html#749"><abbr title="App\Http\Controllers\PlacesController::setDefaultLocation">setDefaultLocation</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#48"><abbr title="App\Http\Controllers\PopupController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PopupController.php.html#90"><abbr title="App\Http\Controllers\PopupController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PostsController.php.html#139"><abbr title="App\Http\Controllers\PostsController::handleIndex">handleIndex</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/PricingController.php.html#37"><abbr title="App\Http\Controllers\PricingController::edit">edit</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#26"><abbr title="App\Http\Controllers\QuotesController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/QuotesController.php.html#132"><abbr title="App\Http\Controllers\QuotesController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#22"><abbr title="App\Http\Controllers\RepeatingClientsController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/RepeatingClientsController.php.html#126"><abbr title="App\Http\Controllers\RepeatingClientsController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#278"><abbr title="App\Http\Controllers\ReservationsController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ReservationsController.php.html#319"><abbr title="App\Http\Controllers\ReservationsController::confirm">confirm</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/SearchController.php.html#21"><abbr title="App\Http\Controllers\SearchController::index">index</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 12:51:21 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([35,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([145,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,10,"<a href=\"Controllers\/AccessoriesController.php.html#10\">App\\Http\\Controllers\\AccessoriesController<\/a>"],[0,5,"<a href=\"Controllers\/Auth\/LoginController.php.html#11\">App\\Http\\Controllers\\Auth\\LoginController<\/a>"],[0,5,"<a href=\"Controllers\/Auth\/TwoFactorController.php.html#9\">App\\Http\\Controllers\\Auth\\TwoFactorController<\/a>"],[0,5,"<a href=\"Controllers\/AuthController.php.html#10\">App\\Http\\Controllers\\AuthController<\/a>"],[0,24,"<a href=\"Controllers\/BookingsController.php.html#10\">App\\Http\\Controllers\\BookingsController<\/a>"],[0,13,"<a href=\"Controllers\/CategoriesController.php.html#10\">App\\Http\\Controllers\\CategoriesController<\/a>"],[0,9,"<a href=\"Controllers\/ContactController.php.html#20\">App\\Http\\Controllers\\ContactController<\/a>"],[0,18,"<a href=\"Controllers\/Controller.php.html#24\">App\\Http\\Controllers\\Controller<\/a>"],[0,12,"<a href=\"Controllers\/CouponsController.php.html#12\">App\\Http\\Controllers\\CouponsController<\/a>"],[0,2,"<a href=\"Controllers\/CustomersController.php.html#8\">App\\Http\\Controllers\\CustomersController<\/a>"],[0,1,"<a href=\"Controllers\/DashboardController.php.html#9\">App\\Http\\Controllers\\DashboardController<\/a>"],[0,5,"<a href=\"Controllers\/FeedController.php.html#8\">App\\Http\\Controllers\\FeedController<\/a>"],[0,15,"<a href=\"Controllers\/FeedbackController.php.html#13\">App\\Http\\Controllers\\FeedbackController<\/a>"],[0,3,"<a href=\"Controllers\/HomeController.php.html#9\">App\\Http\\Controllers\\HomeController<\/a>"],[0,16,"<a href=\"Controllers\/ImageController.php.html#12\">App\\Http\\Controllers\\ImageController<\/a>"],[0,39,"<a href=\"Controllers\/ListingsController.php.html#20\">App\\Http\\Controllers\\ListingsController<\/a>"],[0,10,"<a href=\"Controllers\/LocationsController.php.html#10\">App\\Http\\Controllers\\LocationsController<\/a>"],[0,3,"<a href=\"Controllers\/OffersController.php.html#14\">App\\Http\\Controllers\\OffersController<\/a>"],[0,12,"<a href=\"Controllers\/PagesController.php.html#8\">App\\Http\\Controllers\\PagesController<\/a>"],[0,25,"<a href=\"Controllers\/PlacesController.php.html#12\">App\\Http\\Controllers\\PlacesController<\/a>"],[0,12,"<a href=\"Controllers\/PopupController.php.html#11\">App\\Http\\Controllers\\PopupController<\/a>"],[0,11,"<a href=\"Controllers\/PostsController.php.html#14\">App\\Http\\Controllers\\PostsController<\/a>"],[0,11,"<a href=\"Controllers\/PricingController.php.html#13\">App\\Http\\Controllers\\PricingController<\/a>"],[0,10,"<a href=\"Controllers\/QuotesController.php.html#18\">App\\Http\\Controllers\\QuotesController<\/a>"],[0,11,"<a href=\"Controllers\/RepeatingClientsController.php.html#15\">App\\Http\\Controllers\\RepeatingClientsController<\/a>"],[0,31,"<a href=\"Controllers\/ReservationsController.php.html#20\">App\\Http\\Controllers\\ReservationsController<\/a>"],[0,2,"<a href=\"Controllers\/ReviewsController.php.html#5\">App\\Http\\Controllers\\ReviewsController<\/a>"],[0,1,"<a href=\"Controllers\/RobotsController.php.html#4\">App\\Http\\Controllers\\RobotsController<\/a>"],[0,3,"<a href=\"Controllers\/SearchController.php.html#10\">App\\Http\\Controllers\\SearchController<\/a>"],[0,2,"<a href=\"Controllers\/SessionsController.php.html#7\">App\\Http\\Controllers\\SessionsController<\/a>"],[0,3,"<a href=\"Controllers\/TestController.php.html#9\">App\\Http\\Controllers\\TestController<\/a>"],[0,2,"<a href=\"Requests\/AvailabilityRequest.php.html#7\">App\\Http\\Requests\\AvailabilityRequest<\/a>"],[0,6,"<a href=\"Requests\/ContactRequest.php.html#7\">App\\Http\\Requests\\ContactRequest<\/a>"],[0,2,"<a href=\"Requests\/PricingRequest.php.html#7\">App\\Http\\Requests\\PricingRequest<\/a>"],[100,0,"<a href=\"Requests\/Request.php.html#7\">App\\Http\\Requests\\Request<\/a>"],[0,10,"<a href=\"Requests\/ReservationRequest.php.html#7\">App\\Http\\Requests\\ReservationRequest<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Controllers\/AccessoriesController.php.html#17\">App\\Http\\Controllers\\AccessoriesController::index<\/a>"],[0,1,"<a href=\"Controllers\/AccessoriesController.php.html#32\">App\\Http\\Controllers\\AccessoriesController::create<\/a>"],[0,3,"<a href=\"Controllers\/AccessoriesController.php.html#43\">App\\Http\\Controllers\\AccessoriesController::store<\/a>"],[0,1,"<a href=\"Controllers\/AccessoriesController.php.html#70\">App\\Http\\Controllers\\AccessoriesController::edit<\/a>"],[0,3,"<a href=\"Controllers\/AccessoriesController.php.html#87\">App\\Http\\Controllers\\AccessoriesController::update<\/a>"],[0,1,"<a href=\"Controllers\/AccessoriesController.php.html#114\">App\\Http\\Controllers\\AccessoriesController::destroy<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/LoginController.php.html#38\">App\\Http\\Controllers\\Auth\\LoginController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/LoginController.php.html#43\">App\\Http\\Controllers\\Auth\\LoginController::username<\/a>"],[0,3,"<a href=\"Controllers\/Auth\/LoginController.php.html#48\">App\\Http\\Controllers\\Auth\\LoginController::authenticated<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/TwoFactorController.php.html#16\">App\\Http\\Controllers\\Auth\\TwoFactorController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/TwoFactorController.php.html#21\">App\\Http\\Controllers\\Auth\\TwoFactorController::index<\/a>"],[0,2,"<a href=\"Controllers\/Auth\/TwoFactorController.php.html#26\">App\\Http\\Controllers\\Auth\\TwoFactorController::store<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/TwoFactorController.php.html#46\">App\\Http\\Controllers\\Auth\\TwoFactorController::resend<\/a>"],[0,2,"<a href=\"Controllers\/AuthController.php.html#15\">App\\Http\\Controllers\\AuthController::showLogin<\/a>"],[0,3,"<a href=\"Controllers\/AuthController.php.html#30\">App\\Http\\Controllers\\AuthController::handleLogin<\/a>"],[0,1,"<a href=\"Controllers\/BookingsController.php.html#17\">App\\Http\\Controllers\\BookingsController::getCurrentReservationData<\/a>"],[0,1,"<a href=\"Controllers\/BookingsController.php.html#34\">App\\Http\\Controllers\\BookingsController::showBooking<\/a>"],[0,1,"<a href=\"Controllers\/BookingsController.php.html#57\">App\\Http\\Controllers\\BookingsController::handleBooking<\/a>"],[0,8,"<a href=\"Controllers\/BookingsController.php.html#80\">App\\Http\\Controllers\\BookingsController::fleetIndex<\/a>"],[0,13,"<a href=\"Controllers\/BookingsController.php.html#172\">App\\Http\\Controllers\\BookingsController::initialSetupOfCookie<\/a>"],[0,1,"<a href=\"Controllers\/CategoriesController.php.html#17\">App\\Http\\Controllers\\CategoriesController::index<\/a>"],[0,1,"<a href=\"Controllers\/CategoriesController.php.html#33\">App\\Http\\Controllers\\CategoriesController::create<\/a>"],[0,3,"<a href=\"Controllers\/CategoriesController.php.html#46\">App\\Http\\Controllers\\CategoriesController::store<\/a>"],[0,1,"<a href=\"Controllers\/CategoriesController.php.html#71\">App\\Http\\Controllers\\CategoriesController::edit<\/a>"],[0,5,"<a href=\"Controllers\/CategoriesController.php.html#86\">App\\Http\\Controllers\\CategoriesController::update<\/a>"],[0,2,"<a href=\"Controllers\/CategoriesController.php.html#116\">App\\Http\\Controllers\\CategoriesController::destroy<\/a>"],[0,1,"<a href=\"Controllers\/ContactController.php.html#28\">App\\Http\\Controllers\\ContactController::showContact<\/a>"],[0,5,"<a href=\"Controllers\/ContactController.php.html#73\">App\\Http\\Controllers\\ContactController::handleContact<\/a>"],[0,2,"<a href=\"Controllers\/ContactController.php.html#151\">App\\Http\\Controllers\\ContactController::handleNewsletter<\/a>"],[0,1,"<a href=\"Controllers\/ContactController.php.html#176\">App\\Http\\Controllers\\ContactController::getNewslettersIndex<\/a>"],[0,6,"<a href=\"Controllers\/Controller.php.html#32\">App\\Http\\Controllers\\Controller::__construct<\/a>"],[0,2,"<a href=\"Controllers\/Controller.php.html#132\">App\\Http\\Controllers\\Controller::setupLayout<\/a>"],[0,2,"<a href=\"Controllers\/Controller.php.html#149\">App\\Http\\Controllers\\Controller::overwriteOpengraph<\/a>"],[0,8,"<a href=\"Controllers\/Controller.php.html#165\">App\\Http\\Controllers\\Controller::popup<\/a>"],[0,2,"<a href=\"Controllers\/CouponsController.php.html#21\">App\\Http\\Controllers\\CouponsController::index<\/a>"],[0,1,"<a href=\"Controllers\/CouponsController.php.html#44\">App\\Http\\Controllers\\CouponsController::create<\/a>"],[0,3,"<a href=\"Controllers\/CouponsController.php.html#59\">App\\Http\\Controllers\\CouponsController::store<\/a>"],[0,1,"<a href=\"Controllers\/CouponsController.php.html#88\">App\\Http\\Controllers\\CouponsController::show<\/a>"],[0,1,"<a href=\"Controllers\/CouponsController.php.html#100\">App\\Http\\Controllers\\CouponsController::edit<\/a>"],[0,3,"<a href=\"Controllers\/CouponsController.php.html#118\">App\\Http\\Controllers\\CouponsController::update<\/a>"],[0,1,"<a href=\"Controllers\/CouponsController.php.html#150\">App\\Http\\Controllers\\CouponsController::destroy<\/a>"],[0,2,"<a href=\"Controllers\/CustomersController.php.html#14\">App\\Http\\Controllers\\CustomersController::index<\/a>"],[0,1,"<a href=\"Controllers\/DashboardController.php.html#16\">App\\Http\\Controllers\\DashboardController::getIndex<\/a>"],[0,1,"<a href=\"Controllers\/FeedController.php.html#12\">App\\Http\\Controllers\\FeedController::__construct<\/a>"],[0,4,"<a href=\"Controllers\/FeedController.php.html#25\">App\\Http\\Controllers\\FeedController::exportCarsCsv<\/a>"],[0,5,"<a href=\"Controllers\/FeedbackController.php.html#20\">App\\Http\\Controllers\\FeedbackController::create<\/a>"],[0,8,"<a href=\"Controllers\/FeedbackController.php.html#59\">App\\Http\\Controllers\\FeedbackController::store<\/a>"],[0,1,"<a href=\"Controllers\/FeedbackController.php.html#148\">App\\Http\\Controllers\\FeedbackController::index<\/a>"],[0,1,"<a href=\"Controllers\/FeedbackController.php.html#164\">App\\Http\\Controllers\\FeedbackController::show<\/a>"],[0,1,"<a href=\"Controllers\/HomeController.php.html#12\">App\\Http\\Controllers\\HomeController::__construct<\/a>"],[0,2,"<a href=\"Controllers\/HomeController.php.html#20\">App\\Http\\Controllers\\HomeController::getIndex<\/a>"],[0,5,"<a href=\"Controllers\/ImageController.php.html#18\">App\\Http\\Controllers\\ImageController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/ImageController.php.html#32\">App\\Http\\Controllers\\ImageController::postUpload<\/a>"],[0,2,"<a href=\"Controllers\/ImageController.php.html#40\">App\\Http\\Controllers\\ImageController::deleteUpload<\/a>"],[0,4,"<a href=\"Controllers\/ImageController.php.html#55\">App\\Http\\Controllers\\ImageController::getListingImages<\/a>"],[0,4,"<a href=\"Controllers\/ImageController.php.html#86\">App\\Http\\Controllers\\ImageController::getLocationImages<\/a>"],[0,1,"<a href=\"Controllers\/ListingsController.php.html#24\">App\\Http\\Controllers\\ListingsController::__construct<\/a>"],[0,11,"<a href=\"Controllers\/ListingsController.php.html#36\">App\\Http\\Controllers\\ListingsController::index<\/a>"],[0,14,"<a href=\"Controllers\/ListingsController.php.html#255\">App\\Http\\Controllers\\ListingsController::show<\/a>"],[0,12,"<a href=\"Controllers\/ListingsController.php.html#477\">App\\Http\\Controllers\\ListingsController::getPrice<\/a>"],[0,1,"<a href=\"Controllers\/ListingsController.php.html#550\">App\\Http\\Controllers\\ListingsController::handleUnavailable<\/a>"],[0,1,"<a href=\"Controllers\/LocationsController.php.html#18\">App\\Http\\Controllers\\LocationsController::index<\/a>"],[0,1,"<a href=\"Controllers\/LocationsController.php.html#34\">App\\Http\\Controllers\\LocationsController::create<\/a>"],[0,3,"<a href=\"Controllers\/LocationsController.php.html#47\">App\\Http\\Controllers\\LocationsController::store<\/a>"],[0,1,"<a href=\"Controllers\/LocationsController.php.html#77\">App\\Http\\Controllers\\LocationsController::edit<\/a>"],[0,3,"<a href=\"Controllers\/LocationsController.php.html#93\">App\\Http\\Controllers\\LocationsController::update<\/a>"],[0,1,"<a href=\"Controllers\/LocationsController.php.html#123\">App\\Http\\Controllers\\LocationsController::destroy<\/a>"],[0,3,"<a href=\"Controllers\/OffersController.php.html#23\">App\\Http\\Controllers\\OffersController::show<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#10\">App\\Http\\Controllers\\PagesController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#21\">App\\Http\\Controllers\\PagesController::about<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#61\">App\\Http\\Controllers\\PagesController::offers<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#70\">App\\Http\\Controllers\\PagesController::crete<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#78\">App\\Http\\Controllers\\PagesController::services<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#118\">App\\Http\\Controllers\\PagesController::branches<\/a>"],[0,2,"<a href=\"Controllers\/PagesController.php.html#127\">App\\Http\\Controllers\\PagesController::faq<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#189\">App\\Http\\Controllers\\PagesController::policy<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#229\">App\\Http\\Controllers\\PagesController::privacy<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#260\">App\\Http\\Controllers\\PagesController::safety<\/a>"],[0,1,"<a href=\"Controllers\/PagesController.php.html#293\">App\\Http\\Controllers\\PagesController::insurance<\/a>"],[0,1,"<a href=\"Controllers\/PlacesController.php.html#32\">App\\Http\\Controllers\\PlacesController::__construct<\/a>"],[0,4,"<a href=\"Controllers\/PlacesController.php.html#74\">App\\Http\\Controllers\\PlacesController::heraklion<\/a>"],[0,3,"<a href=\"Controllers\/PlacesController.php.html#184\">App\\Http\\Controllers\\PlacesController::heraklion_airport<\/a>"],[0,3,"<a href=\"Controllers\/PlacesController.php.html#298\">App\\Http\\Controllers\\PlacesController::chania_airport<\/a>"],[0,4,"<a href=\"Controllers\/PlacesController.php.html#411\">App\\Http\\Controllers\\PlacesController::chania<\/a>"],[0,4,"<a href=\"Controllers\/PlacesController.php.html#522\">App\\Http\\Controllers\\PlacesController::rethymno<\/a>"],[0,4,"<a href=\"Controllers\/PlacesController.php.html#634\">App\\Http\\Controllers\\PlacesController::agios_nikolaos<\/a>"],[0,2,"<a href=\"Controllers\/PlacesController.php.html#749\">App\\Http\\Controllers\\PlacesController::setDefaultLocation<\/a>"],[0,1,"<a href=\"Controllers\/PopupController.php.html#18\">App\\Http\\Controllers\\PopupController::index<\/a>"],[0,1,"<a href=\"Controllers\/PopupController.php.html#33\">App\\Http\\Controllers\\PopupController::create<\/a>"],[0,2,"<a href=\"Controllers\/PopupController.php.html#48\">App\\Http\\Controllers\\PopupController::store<\/a>"],[0,1,"<a href=\"Controllers\/PopupController.php.html#72\">App\\Http\\Controllers\\PopupController::edit<\/a>"],[0,2,"<a href=\"Controllers\/PopupController.php.html#90\">App\\Http\\Controllers\\PopupController::update<\/a>"],[0,1,"<a href=\"Controllers\/PopupController.php.html#114\">App\\Http\\Controllers\\PopupController::destroy<\/a>"],[0,4,"<a href=\"Controllers\/PopupController.php.html#130\">App\\Http\\Controllers\\PopupController::return_bytes<\/a>"],[0,1,"<a href=\"Controllers\/PostsController.php.html#16\">App\\Http\\Controllers\\PostsController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/PostsController.php.html#28\">App\\Http\\Controllers\\PostsController::index<\/a>"],[0,1,"<a href=\"Controllers\/PostsController.php.html#37\">App\\Http\\Controllers\\PostsController::indexWithTags<\/a>"],[0,6,"<a href=\"Controllers\/PostsController.php.html#48\">App\\Http\\Controllers\\PostsController::show<\/a>"],[0,2,"<a href=\"Controllers\/PostsController.php.html#139\">App\\Http\\Controllers\\PostsController::handleIndex<\/a>"],[0,1,"<a href=\"Controllers\/PricingController.php.html#20\">App\\Http\\Controllers\\PricingController::index<\/a>"],[0,2,"<a href=\"Controllers\/PricingController.php.html#37\">App\\Http\\Controllers\\PricingController::edit<\/a>"],[0,4,"<a href=\"Controllers\/PricingController.php.html#73\">App\\Http\\Controllers\\PricingController::update<\/a>"],[0,4,"<a href=\"Controllers\/PricingController.php.html#113\">App\\Http\\Controllers\\PricingController::availabilityUpdate<\/a>"],[0,2,"<a href=\"Controllers\/QuotesController.php.html#26\">App\\Http\\Controllers\\QuotesController::index<\/a>"],[0,1,"<a href=\"Controllers\/QuotesController.php.html#47\">App\\Http\\Controllers\\QuotesController::create<\/a>"],[0,3,"<a href=\"Controllers\/QuotesController.php.html#57\">App\\Http\\Controllers\\QuotesController::store<\/a>"],[0,1,"<a href=\"Controllers\/QuotesController.php.html#112\">App\\Http\\Controllers\\QuotesController::edit<\/a>"],[0,2,"<a href=\"Controllers\/QuotesController.php.html#132\">App\\Http\\Controllers\\QuotesController::update<\/a>"],[0,1,"<a href=\"Controllers\/QuotesController.php.html#156\">App\\Http\\Controllers\\QuotesController::destroy<\/a>"],[0,2,"<a href=\"Controllers\/RepeatingClientsController.php.html#22\">App\\Http\\Controllers\\RepeatingClientsController::index<\/a>"],[0,1,"<a href=\"Controllers\/RepeatingClientsController.php.html#44\">App\\Http\\Controllers\\RepeatingClientsController::create<\/a>"],[0,3,"<a href=\"Controllers\/RepeatingClientsController.php.html#55\">App\\Http\\Controllers\\RepeatingClientsController::store<\/a>"],[0,1,"<a href=\"Controllers\/RepeatingClientsController.php.html#95\">App\\Http\\Controllers\\RepeatingClientsController::show<\/a>"],[0,1,"<a href=\"Controllers\/RepeatingClientsController.php.html#114\">App\\Http\\Controllers\\RepeatingClientsController::edit<\/a>"],[0,2,"<a href=\"Controllers\/RepeatingClientsController.php.html#126\">App\\Http\\Controllers\\RepeatingClientsController::update<\/a>"],[0,1,"<a href=\"Controllers\/RepeatingClientsController.php.html#151\">App\\Http\\Controllers\\RepeatingClientsController::destroy<\/a>"],[0,16,"<a href=\"Controllers\/ReservationsController.php.html#27\">App\\Http\\Controllers\\ReservationsController::index<\/a>"],[0,1,"<a href=\"Controllers\/ReservationsController.php.html#160\">App\\Http\\Controllers\\ReservationsController::create<\/a>"],[0,3,"<a href=\"Controllers\/ReservationsController.php.html#171\">App\\Http\\Controllers\\ReservationsController::store<\/a>"],[0,1,"<a href=\"Controllers\/ReservationsController.php.html#246\">App\\Http\\Controllers\\ReservationsController::show<\/a>"],[0,1,"<a href=\"Controllers\/ReservationsController.php.html#266\">App\\Http\\Controllers\\ReservationsController::edit<\/a>"],[0,2,"<a href=\"Controllers\/ReservationsController.php.html#278\">App\\Http\\Controllers\\ReservationsController::update<\/a>"],[0,1,"<a href=\"Controllers\/ReservationsController.php.html#299\">App\\Http\\Controllers\\ReservationsController::destroy<\/a>"],[0,2,"<a href=\"Controllers\/ReservationsController.php.html#319\">App\\Http\\Controllers\\ReservationsController::confirm<\/a>"],[0,4,"<a href=\"Controllers\/ReservationsController.php.html#343\">App\\Http\\Controllers\\ReservationsController::verify<\/a>"],[0,1,"<a href=\"Controllers\/ReviewsController.php.html#8\">App\\Http\\Controllers\\ReviewsController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/ReviewsController.php.html#16\">App\\Http\\Controllers\\ReviewsController::index<\/a>"],[0,1,"<a href=\"Controllers\/RobotsController.php.html#6\">App\\Http\\Controllers\\RobotsController::robots<\/a>"],[0,1,"<a href=\"Controllers\/SearchController.php.html#13\">App\\Http\\Controllers\\SearchController::__construct<\/a>"],[0,2,"<a href=\"Controllers\/SearchController.php.html#21\">App\\Http\\Controllers\\SearchController::index<\/a>"],[0,1,"<a href=\"Controllers\/SessionsController.php.html#14\">App\\Http\\Controllers\\SessionsController::store<\/a>"],[0,1,"<a href=\"Controllers\/SessionsController.php.html#28\">App\\Http\\Controllers\\SessionsController::destroyAll<\/a>"],[0,1,"<a href=\"Controllers\/TestController.php.html#12\">App\\Http\\Controllers\\TestController::offerEmail<\/a>"],[0,1,"<a href=\"Controllers\/TestController.php.html#33\">App\\Http\\Controllers\\TestController::deepl<\/a>"],[0,1,"<a href=\"Controllers\/TestController.php.html#48\">App\\Http\\Controllers\\TestController::multiMail<\/a>"],[0,1,"<a href=\"Requests\/AvailabilityRequest.php.html#14\">App\\Http\\Requests\\AvailabilityRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/AvailabilityRequest.php.html#24\">App\\Http\\Requests\\AvailabilityRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ContactRequest.php.html#14\">App\\Http\\Requests\\ContactRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/ContactRequest.php.html#24\">App\\Http\\Requests\\ContactRequest::rules<\/a>"],[0,4,"<a href=\"Requests\/ContactRequest.php.html#31\">App\\Http\\Requests\\ContactRequest::sanitize<\/a>"],[0,1,"<a href=\"Requests\/PricingRequest.php.html#14\">App\\Http\\Requests\\PricingRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/PricingRequest.php.html#24\">App\\Http\\Requests\\PricingRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ReservationRequest.php.html#14\">App\\Http\\Requests\\ReservationRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/ReservationRequest.php.html#24\">App\\Http\\Requests\\ReservationRequest::rules<\/a>"],[0,8,"<a href=\"Requests\/ReservationRequest.php.html#31\">App\\Http\\Requests\\ReservationRequest::sanitize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

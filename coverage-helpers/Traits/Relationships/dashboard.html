<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Traits/Relationships</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Traits</a></li>
         <li class="breadcrumb-item"><a href="index.html">Relationships</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CategoryRelationships.php.html#5">App\Traits\Relationships\CategoryRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomerRelationships.php.html#5">App\Traits\Relationships\CustomerRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedbackRelationships.php.html#5">App\Traits\Relationships\FeedbackRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewRelationships.php.html#9">App\Traits\Relationships\GoogleReviewRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupRelationships.php.html#8">App\Traits\Relationships\GroupRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageRelationships.php.html#5">App\Traits\Relationships\ImageRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LanguageRelationships.php.html#7">App\Traits\Relationships\LanguageRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#12">App\Traits\Relationships\ListingRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisedMotifRelationships.php.html#8">App\Traits\Relationships\LocalisedMotifRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationRelationships.php.html#7">App\Traits\Relationships\LocationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MotifRelationships.php.html#10">App\Traits\Relationships\MotifRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#14">App\Traits\Relationships\OfferRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PeriodRelationships.php.html#8">App\Traits\Relationships\PeriodRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#12">App\Traits\Relationships\PostRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslationRelationships.php.html#7">App\Traits\Relationships\PostTranslationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteRelationships.php.html#5">App\Traits\Relationships\QuoteRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationRelationships.php.html#9">App\Traits\Relationships\ReservationRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupergroupRelationships.php.html#10">App\Traits\Relationships\SupergroupRelationships</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagRelationships.php.html#8">App\Traits\Relationships\TagRelationships</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PostRelationships.php.html#12">App\Traits\Relationships\PostRelationships</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="LocationRelationships.php.html#7">App\Traits\Relationships\LocationRelationships</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CategoryRelationships.php.html#12"><abbr title="App\Traits\Relationships\CategoryRelationships::publishedListings">publishedListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomerRelationships.php.html#11"><abbr title="App\Traits\Relationships\CustomerRelationships::reservations">reservations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomerRelationships.php.html#19"><abbr title="App\Traits\Relationships\CustomerRelationships::quotes">quotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedbackRelationships.php.html#12"><abbr title="App\Traits\Relationships\FeedbackRelationships::reservation">reservation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewRelationships.php.html#14"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::reservation">reservation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewRelationships.php.html#22"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::location">location</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewRelationships.php.html#30"><abbr title="App\Traits\Relationships\GoogleReviewRelationships::language">language</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupRelationships.php.html#11"><abbr title="App\Traits\Relationships\GroupRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupRelationships.php.html#16"><abbr title="App\Traits\Relationships\GroupRelationships::relatedGroups">relatedGroups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupRelationships.php.html#26"><abbr title="App\Traits\Relationships\GroupRelationships::periods">periods</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupRelationships.php.html#34"><abbr title="App\Traits\Relationships\GroupRelationships::supergroup">supergroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageRelationships.php.html#8"><abbr title="App\Traits\Relationships\ImageRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageRelationships.php.html#13"><abbr title="App\Traits\Relationships\ImageRelationships::locations">locations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LanguageRelationships.php.html#13"><abbr title="App\Traits\Relationships\LanguageRelationships::googleReviews">googleReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#15"><abbr title="App\Traits\Relationships\ListingRelationships::group">group</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#20"><abbr title="App\Traits\Relationships\ListingRelationships::categories">categories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#25"><abbr title="App\Traits\Relationships\ListingRelationships::getCategoryList">getCategoryList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#30"><abbr title="App\Traits\Relationships\ListingRelationships::images">images</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#43"><abbr title="App\Traits\Relationships\ListingRelationships::related">related</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#60"><abbr title="App\Traits\Relationships\ListingRelationships::suggested">suggested</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#79"><abbr title="App\Traits\Relationships\ListingRelationships::similar">similar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#87"><abbr title="App\Traits\Relationships\ListingRelationships::motifs">motifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#98"><abbr title="App\Traits\Relationships\ListingRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingRelationships.php.html#111"><abbr title="App\Traits\Relationships\ListingRelationships::presentablePosts">presentablePosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisedMotifRelationships.php.html#14"><abbr title="App\Traits\Relationships\LocalisedMotifRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisedMotifRelationships.php.html#22"><abbr title="App\Traits\Relationships\LocalisedMotifRelationships::publishedPosts">publishedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationRelationships.php.html#10"><abbr title="App\Traits\Relationships\LocationRelationships::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationRelationships.php.html#18"><abbr title="App\Traits\Relationships\LocationRelationships::images">images</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocationRelationships.php.html#26"><abbr title="App\Traits\Relationships\LocationRelationships::googleReviews">googleReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MotifRelationships.php.html#16"><abbr title="App\Traits\Relationships\MotifRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MotifRelationships.php.html#24"><abbr title="App\Traits\Relationships\MotifRelationships::publishedPosts">publishedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MotifRelationships.php.html#34"><abbr title="App\Traits\Relationships\MotifRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MotifRelationships.php.html#43"><abbr title="App\Traits\Relationships\MotifRelationships::publishedListings">publishedListings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#21"><abbr title="App\Traits\Relationships\OfferRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#32"><abbr title="App\Traits\Relationships\OfferRelationships::customer">customer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#43"><abbr title="App\Traits\Relationships\OfferRelationships::listing">listing</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#52"><abbr title="App\Traits\Relationships\OfferRelationships::pickup_loc">pickup_loc</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#61"><abbr title="App\Traits\Relationships\OfferRelationships::dropoff_loc">dropoff_loc</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#70"><abbr title="App\Traits\Relationships\OfferRelationships::accessories">accessories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OfferRelationships.php.html#79"><abbr title="App\Traits\Relationships\OfferRelationships::reservations">reservations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PeriodRelationships.php.html#16"><abbr title="App\Traits\Relationships\PeriodRelationships::groups">groups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PeriodRelationships.php.html#24"><abbr title="App\Traits\Relationships\PeriodRelationships::changes">changes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PeriodRelationships.php.html#32"><abbr title="App\Traits\Relationships\PeriodRelationships::changesGroup">changesGroup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PeriodRelationships.php.html#41"><abbr title="App\Traits\Relationships\PeriodRelationships::changesGroupYear">changesGroupYear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#18"><abbr title="App\Traits\Relationships\PostRelationships::tags">tags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#28"><abbr title="App\Traits\Relationships\PostRelationships::motifs">motifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#37"><abbr title="App\Traits\Relationships\PostRelationships::localisedMotifs">localisedMotifs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#43"><abbr title="App\Traits\Relationships\PostRelationships::getMotifedPosts">getMotifedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostRelationships.php.html#72"><abbr title="App\Traits\Relationships\PostRelationships::getRelatedPosts">getRelatedPosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslationRelationships.php.html#13"><abbr title="App\Traits\Relationships\PostTranslationRelationships::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostTranslationRelationships.php.html#21"><abbr title="App\Traits\Relationships\PostTranslationRelationships::publishedPost">publishedPost</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteRelationships.php.html#12"><abbr title="App\Traits\Relationships\QuoteRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationRelationships.php.html#16"><abbr title="App\Traits\Relationships\ReservationRelationships::getCustomer">getCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationRelationships.php.html#25"><abbr title="App\Traits\Relationships\ReservationRelationships::feedback">feedback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationRelationships.php.html#34"><abbr title="App\Traits\Relationships\ReservationRelationships::googleReview">googleReview</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationRelationships.php.html#43"><abbr title="App\Traits\Relationships\ReservationRelationships::offer">offer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupergroupRelationships.php.html#15"><abbr title="App\Traits\Relationships\SupergroupRelationships::groups">groups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupergroupRelationships.php.html#23"><abbr title="App\Traits\Relationships\SupergroupRelationships::listings">listings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagRelationships.php.html#13"><abbr title="App\Traits\Relationships\TagRelationships::posts">posts</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PostRelationships.php.html#72"><abbr title="App\Traits\Relationships\PostRelationships::getRelatedPosts">getRelatedPosts</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="LocationRelationships.php.html#10"><abbr title="App\Traits\Relationships\LocationRelationships::getImageAttribute">getImageAttribute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 12:51:21 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([19,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([59,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"CategoryRelationships.php.html#5\">App\\Traits\\Relationships\\CategoryRelationships<\/a>"],[0,2,"<a href=\"CustomerRelationships.php.html#5\">App\\Traits\\Relationships\\CustomerRelationships<\/a>"],[0,1,"<a href=\"FeedbackRelationships.php.html#5\">App\\Traits\\Relationships\\FeedbackRelationships<\/a>"],[0,3,"<a href=\"GoogleReviewRelationships.php.html#9\">App\\Traits\\Relationships\\GoogleReviewRelationships<\/a>"],[0,4,"<a href=\"GroupRelationships.php.html#8\">App\\Traits\\Relationships\\GroupRelationships<\/a>"],[0,2,"<a href=\"ImageRelationships.php.html#5\">App\\Traits\\Relationships\\ImageRelationships<\/a>"],[0,1,"<a href=\"LanguageRelationships.php.html#7\">App\\Traits\\Relationships\\LanguageRelationships<\/a>"],[0,10,"<a href=\"ListingRelationships.php.html#12\">App\\Traits\\Relationships\\ListingRelationships<\/a>"],[0,2,"<a href=\"LocalisedMotifRelationships.php.html#8\">App\\Traits\\Relationships\\LocalisedMotifRelationships<\/a>"],[0,4,"<a href=\"LocationRelationships.php.html#7\">App\\Traits\\Relationships\\LocationRelationships<\/a>"],[0,4,"<a href=\"MotifRelationships.php.html#10\">App\\Traits\\Relationships\\MotifRelationships<\/a>"],[0,7,"<a href=\"OfferRelationships.php.html#14\">App\\Traits\\Relationships\\OfferRelationships<\/a>"],[0,4,"<a href=\"PeriodRelationships.php.html#8\">App\\Traits\\Relationships\\PeriodRelationships<\/a>"],[100,0,"<a href=\"PhotoRelationships.php.html#5\">App\\Traits\\Relationships\\PhotoRelationships<\/a>"],[0,13,"<a href=\"PostRelationships.php.html#12\">App\\Traits\\Relationships\\PostRelationships<\/a>"],[0,2,"<a href=\"PostTranslationRelationships.php.html#7\">App\\Traits\\Relationships\\PostTranslationRelationships<\/a>"],[0,1,"<a href=\"QuoteRelationships.php.html#5\">App\\Traits\\Relationships\\QuoteRelationships<\/a>"],[0,4,"<a href=\"ReservationRelationships.php.html#9\">App\\Traits\\Relationships\\ReservationRelationships<\/a>"],[0,2,"<a href=\"SupergroupRelationships.php.html#10\">App\\Traits\\Relationships\\SupergroupRelationships<\/a>"],[0,1,"<a href=\"TagRelationships.php.html#8\">App\\Traits\\Relationships\\TagRelationships<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"CategoryRelationships.php.html#12\">App\\Traits\\Relationships\\CategoryRelationships::publishedListings<\/a>"],[0,1,"<a href=\"CustomerRelationships.php.html#11\">App\\Traits\\Relationships\\CustomerRelationships::reservations<\/a>"],[0,1,"<a href=\"CustomerRelationships.php.html#19\">App\\Traits\\Relationships\\CustomerRelationships::quotes<\/a>"],[0,1,"<a href=\"FeedbackRelationships.php.html#12\">App\\Traits\\Relationships\\FeedbackRelationships::reservation<\/a>"],[0,1,"<a href=\"GoogleReviewRelationships.php.html#14\">App\\Traits\\Relationships\\GoogleReviewRelationships::reservation<\/a>"],[0,1,"<a href=\"GoogleReviewRelationships.php.html#22\">App\\Traits\\Relationships\\GoogleReviewRelationships::location<\/a>"],[0,1,"<a href=\"GoogleReviewRelationships.php.html#30\">App\\Traits\\Relationships\\GoogleReviewRelationships::language<\/a>"],[0,1,"<a href=\"GroupRelationships.php.html#11\">App\\Traits\\Relationships\\GroupRelationships::listings<\/a>"],[0,1,"<a href=\"GroupRelationships.php.html#16\">App\\Traits\\Relationships\\GroupRelationships::relatedGroups<\/a>"],[0,1,"<a href=\"GroupRelationships.php.html#26\">App\\Traits\\Relationships\\GroupRelationships::periods<\/a>"],[0,1,"<a href=\"GroupRelationships.php.html#34\">App\\Traits\\Relationships\\GroupRelationships::supergroup<\/a>"],[0,1,"<a href=\"ImageRelationships.php.html#8\">App\\Traits\\Relationships\\ImageRelationships::listings<\/a>"],[0,1,"<a href=\"ImageRelationships.php.html#13\">App\\Traits\\Relationships\\ImageRelationships::locations<\/a>"],[0,1,"<a href=\"LanguageRelationships.php.html#13\">App\\Traits\\Relationships\\LanguageRelationships::googleReviews<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#15\">App\\Traits\\Relationships\\ListingRelationships::group<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#20\">App\\Traits\\Relationships\\ListingRelationships::categories<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#25\">App\\Traits\\Relationships\\ListingRelationships::getCategoryList<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#30\">App\\Traits\\Relationships\\ListingRelationships::images<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#43\">App\\Traits\\Relationships\\ListingRelationships::related<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#60\">App\\Traits\\Relationships\\ListingRelationships::suggested<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#79\">App\\Traits\\Relationships\\ListingRelationships::similar<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#87\">App\\Traits\\Relationships\\ListingRelationships::motifs<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#98\">App\\Traits\\Relationships\\ListingRelationships::posts<\/a>"],[0,1,"<a href=\"ListingRelationships.php.html#111\">App\\Traits\\Relationships\\ListingRelationships::presentablePosts<\/a>"],[0,1,"<a href=\"LocalisedMotifRelationships.php.html#14\">App\\Traits\\Relationships\\LocalisedMotifRelationships::posts<\/a>"],[0,1,"<a href=\"LocalisedMotifRelationships.php.html#22\">App\\Traits\\Relationships\\LocalisedMotifRelationships::publishedPosts<\/a>"],[0,2,"<a href=\"LocationRelationships.php.html#10\">App\\Traits\\Relationships\\LocationRelationships::getImageAttribute<\/a>"],[0,1,"<a href=\"LocationRelationships.php.html#18\">App\\Traits\\Relationships\\LocationRelationships::images<\/a>"],[0,1,"<a href=\"LocationRelationships.php.html#26\">App\\Traits\\Relationships\\LocationRelationships::googleReviews<\/a>"],[0,1,"<a href=\"MotifRelationships.php.html#16\">App\\Traits\\Relationships\\MotifRelationships::posts<\/a>"],[0,1,"<a href=\"MotifRelationships.php.html#24\">App\\Traits\\Relationships\\MotifRelationships::publishedPosts<\/a>"],[0,1,"<a href=\"MotifRelationships.php.html#34\">App\\Traits\\Relationships\\MotifRelationships::listings<\/a>"],[0,1,"<a href=\"MotifRelationships.php.html#43\">App\\Traits\\Relationships\\MotifRelationships::publishedListings<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#21\">App\\Traits\\Relationships\\OfferRelationships::getCustomer<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#32\">App\\Traits\\Relationships\\OfferRelationships::customer<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#43\">App\\Traits\\Relationships\\OfferRelationships::listing<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#52\">App\\Traits\\Relationships\\OfferRelationships::pickup_loc<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#61\">App\\Traits\\Relationships\\OfferRelationships::dropoff_loc<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#70\">App\\Traits\\Relationships\\OfferRelationships::accessories<\/a>"],[0,1,"<a href=\"OfferRelationships.php.html#79\">App\\Traits\\Relationships\\OfferRelationships::reservations<\/a>"],[0,1,"<a href=\"PeriodRelationships.php.html#16\">App\\Traits\\Relationships\\PeriodRelationships::groups<\/a>"],[0,1,"<a href=\"PeriodRelationships.php.html#24\">App\\Traits\\Relationships\\PeriodRelationships::changes<\/a>"],[0,1,"<a href=\"PeriodRelationships.php.html#32\">App\\Traits\\Relationships\\PeriodRelationships::changesGroup<\/a>"],[0,1,"<a href=\"PeriodRelationships.php.html#41\">App\\Traits\\Relationships\\PeriodRelationships::changesGroupYear<\/a>"],[0,1,"<a href=\"PostRelationships.php.html#18\">App\\Traits\\Relationships\\PostRelationships::tags<\/a>"],[0,1,"<a href=\"PostRelationships.php.html#28\">App\\Traits\\Relationships\\PostRelationships::motifs<\/a>"],[0,1,"<a href=\"PostRelationships.php.html#37\">App\\Traits\\Relationships\\PostRelationships::localisedMotifs<\/a>"],[0,1,"<a href=\"PostRelationships.php.html#43\">App\\Traits\\Relationships\\PostRelationships::getMotifedPosts<\/a>"],[0,9,"<a href=\"PostRelationships.php.html#72\">App\\Traits\\Relationships\\PostRelationships::getRelatedPosts<\/a>"],[0,1,"<a href=\"PostTranslationRelationships.php.html#13\">App\\Traits\\Relationships\\PostTranslationRelationships::post<\/a>"],[0,1,"<a href=\"PostTranslationRelationships.php.html#21\">App\\Traits\\Relationships\\PostTranslationRelationships::publishedPost<\/a>"],[0,1,"<a href=\"QuoteRelationships.php.html#12\">App\\Traits\\Relationships\\QuoteRelationships::getCustomer<\/a>"],[0,1,"<a href=\"ReservationRelationships.php.html#16\">App\\Traits\\Relationships\\ReservationRelationships::getCustomer<\/a>"],[0,1,"<a href=\"ReservationRelationships.php.html#25\">App\\Traits\\Relationships\\ReservationRelationships::feedback<\/a>"],[0,1,"<a href=\"ReservationRelationships.php.html#34\">App\\Traits\\Relationships\\ReservationRelationships::googleReview<\/a>"],[0,1,"<a href=\"ReservationRelationships.php.html#43\">App\\Traits\\Relationships\\ReservationRelationships::offer<\/a>"],[0,1,"<a href=\"SupergroupRelationships.php.html#15\">App\\Traits\\Relationships\\SupergroupRelationships::groups<\/a>"],[0,1,"<a href=\"SupergroupRelationships.php.html#23\">App\\Traits\\Relationships\\SupergroupRelationships::listings<\/a>"],[0,1,"<a href=\"TagRelationships.php.html#13\">App\\Traits\\Relationships\\TagRelationships::posts<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<h2 class="text-2xl lg:text-3xl">{{ __('home.featured_listings') }}</h2>

<div>
    <div class="listing-swiper relative lg:w-4/6 ml-4 lg:mx-auto mt-8 lg:pb-12 lg:overflow-hidden">
        <div class="swiper-wrapper">
            @foreach ($featuredListings as $featuredListing)
                <div class="swiper-slide shadow-lg border border-lightgray rounded-lg p-3 !h-auto">

                    {{-- Listing heading --}}
                    <div class="flex justify-between">
                        <div class="h-24">
                            <div class="text-2xl font-geo-black">{{ $featuredListing->SEOshortTitle }}</div>

                            <div class="uppercase text-mediumgray text-sm">{{ __('bookings.fleet.or_similar') }}</div>
                        </div>
                        <div class="text-mediumgray text-sm">
                            GROUP {{ $featuredListing->group->name }}
                        </div>
                    </div>

                    {{-- Listing Image --}}
                    {{ $featuredListing->getFirstMedia('photos') }}

                    {{-- Hot offer - Best Seller --}}
                    <div class="flex justify-between mb-6">
                        <div>
                            {{-- @if ($featuredListing->group->on_offer) --}}
                            <span class="text-eurodefault font-bold text-xs leading-none block">HOT <br />
                                OFFER</span>
                            {{-- @endif --}}
                        </div>
                        <div>
                            {{-- @if ($featuredListing->popular) --}}
                            <span class="text-eurogray text-right font-bold text-xs leading-none block">BEST <br />
                                SELLER</span>
                            {{-- @endif --}}

                        </div>
                    </div>

                    {{-- Listing Properties --}}
                    <div class="flex flex-wrap justify-between gap-2 py-3 px-2 border-t border-b uppercase text-sm">

                        {{-- Passengers --}}
                        <div class="w-2/5 grow flex justify-start gap-4 items-center">
                            <img src="{!! asset('sansa/icons/car_seat.svg') !!}" alt="passengers icon" class="w-4" />
                            <div>{!! $featuredListing->seats !!}</div>
                        </div>

                        {{-- Transmission --}}
                        <div class="w-2/5 grow flex justify-start gap-4 items-center">
                            <img src="{!! asset('sansa/icons/gears.svg') !!}" alt="transmission icon" class="w-4" />
                            <div>{!! $featuredListing->transmission !!}</div>
                        </div>

                        {{-- Motor --}}
                        <div class="w-2/5 grow flex justify-start gap-4 items-center">
                            <img src="{!! asset('sansa/icons/motor.svg') !!}" alt="motor icon" class="w-4" />
                            <div>{!! $featuredListing->engine !!}</div>
                        </div>

                        {{-- Air Condition --}}
                        @if ($featuredListing->clima)
                            <div class="w-2/5 grow flex justify-start gap-4 items-center">
                                <img src="{!! asset('sansa/icons/ac.svg') !!}" alt="air condition icon" class="w-4" />
                                <div>{{ trans('common.yes') }}</div>
                            </div>
                        @endif
                    </div>

                    {{-- Price and CTA --}}
                    <div class="mt-4">
                        <div class="uppercase text-xs text-center">
                            @if ($featuredListing->group->base_price)
                                <span class="text-mediumgray">{!! Lang::get('listings.from') !!}</span>
                                <span class="text-2xl font-geo-black">{!! $featuredListing->group->base_price !!}<span
                                        class="text-sm">&euro;</span></span>
                                <span class="text-mediumgray">{!! __('listings.per_day') !!}</span>
                            @endif
                        </div>

                        <div>
                            <x-sansa.form.primary-cta href="{!! route('listings.show', [$featuredListing->slug, 'ref_' => $url_ref]) !!}" class="text-xs">
                                {!! __('listings.featured_listings_slider.rent_it') !!}
                            </x-sansa.form.primary-cta>
                        </div>
                    </div>

                </div>
            @endforeach

        </div>


        <div class="swiper-pagination hidden lg:block"></div>


    </div>

    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
</div>

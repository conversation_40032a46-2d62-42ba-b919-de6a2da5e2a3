<div x-show="{{ $action }}LocationModal" x-cloak @click.outside="{{ $action }}LocationModal = false"
    class="bg-black/30 lg:bg-transparent border-1 border-white lg:border-none rounded-lg shadow-lg lg:shadow-none font-normal fixed lg:absolute top-0 left-0 right-0 bottom-0 z-20 lg:-mt-24"
    :style="{{ $action }}LocationModal && window.innerWidth >= 1024 && dates_position_bottom ? `top: ${dates_position_bottom}px` : ''"
    >

    <div x-show="{{ $action }}LocationModal" x-cloak x-transition:enter="transition ease-out duration-400"
        x-transition:enter-start="translate-y-full opacity-0" x-transition:enter-end="translate-y-0 opacity-100"
        class="bg-white rounded-tl-xl rounded-tr-xl fixed lg:relative top-[70px] lg:top-0 left-0 right-0 bottom-0 z-50 overflow-y-scroll transform">

        <div class="sticky top-0 p-3 text-center bg-eurodefault lg:bg-white rounded-tl-xl rounded-tr-xl mb-8 lg:mb-1">
            <h3
                class="lg:hidden"
                x-text="differentDropoff ? `{{ __('forms.pickup_location_full') }}` : `{{ __('forms.pickup_dropoff_location_combined') }}`">
            </h3>
            {{-- Closing modal --}}
            <div @click="{{ $action }}LocationModal = false" class="absolute top-3 right-3">
                <x-sansa.icons.close-icon class="w-6 " />
            </div>
        </div>

        <div class="lg:w-11/12 xl:w-4/5 2xl:w-4/6 mx-3 lg:mx-auto">
            {{-- Show selected Pickup Location --}}
            <div x-text="{{ $action }}_location_name" class="bg-eurodefault py-2 px-2 rounded-md">

            </div>
            <div class="lg:flex justify-between">
                @foreach ($locations_dropdown_grouped as $key => $group)
                <div class="lg:flex flex-col">    
                    <div class="text-eurodefault font-bold my-2 px-2 py-2 rounded-lg tex-lg">{{ $key }}</div>
                    
                    @foreach ($group as $id => $item)
                        <div class="ml-3 my-1 py-2 px-2 cursor-pointer {{ Arr::get($_COOKIE, $action . '_location') == $id ? 'font-bold underline' : '' }}"
                            {{-- On click change cookies' values, close modal and update input field value  --}}
                            @click="() => {
                                if (differentDropoff) {
                                    Cookies.set('{{ $action }}_location', {{ $id }});
                                } else {
                                    Cookies.set('pickup_location', {{ $id }});
                                    Cookies.set('dropoff_location', {{ $id }});
                                }
                                {{ $action }}LocationModal = false;
                                {{ $action }}_location_name = `{{ $item }}`;
                            }"
                            {{ $action }}LocationModal=false;
                            {{ $action }}_location_name=`{{ $item }}` "
                        >
                            {{ $item }}
                        </div>
                    @endforeach
                    </div>
                @endforeach
            </div>
        </div>

    </div>

</div>

<div id="dates-modal" x-show="datesModal || timesModal" x-cloak @click.outside="datesModal = false; timesModal=false;"
    class="bg-black/30 lg:bg-transparent border-1 border-white lg:border-none rounded-lg shadow-lg font-normal fixed lg:absolute top-0 left-0 right-0 bottom-0 lg:bottom-auto z-20 lg:-mt-24"
    :style="datesModal || timesModal && window.innerWidth >= 1024 && dates_position_bottom ? `top: ${dates_position_bottom}px` : ''">

    <div x-show="datesModal || timesModal" x-cloak x-transition:enter="transition ease-out duration-400"
        x-transition:enter-start="translate-y-full opacity-0" x-transition:enter-end="translate-y-0 opacity-100"
        class="bg-white rounded-tl-xl rounded-tr-xl fixed lg:relative top-[70px] lg:top-0 left-0 right-0 bottom-0 lg:bottom-auto z-50 overflow-y-scroll transform lg:pb-6">

        <div class="sticky z-10 top-0 p-3 bg-eurodefault rounded-tl-xl rounded-tr-xl mb-3 lg:hidden">

            <div class="flex gap-3 justify-evenly items-center">
                <div>
                    <div>{{ __('forms.pickup') }}</div>
                    <div id="modal-pickup-date" x-text="pickup_date"></div>
                    <div x-text="pickup_time"></div>
                </div>
                <div>
                    <span class="text-xl">&#10143;</span>
                </div>
                <div>
                    <div>{{ __('forms.dropoff') }}</div>
                    <div id="modal-dropoff-date" x-text="dropoff_date"></div>
                    <div x-text="dropoff_time"></div>
                </div>
            </div>

            {{-- Closing modal --}}
            <div @click="datesModal = false; timesModal = false;" class="absolute top-3 right-3">
                <x-sansa.icons.close-icon class="w-6 " />
            </div>
        </div>

        <div x-show="datesModal && !timesModal" x-cloak class="relative" id="dates-calendar">
            <div id="datepicker" data-lang="{{ $current_locale }}"></div>
        </div>

        <div x-show="timesModal && !datesModal" x-cloak x-ref="scrollContainer" class="relative lg:w-11/12 xl:w-4/6 p-2 mx-auto"
            id="hours-list">
            <div class="flex gap-1 lg:gap-12">

                <div x-data="{
                    scrollToPickupTime() {
                        const selectedPickupTime = document.querySelector(`[data-pickup-time='${this.pickup_time}']`);
                
                        selectedPickupTime.scrollIntoView({
                            behavior: 'auto',
                            block: 'center',
                            inline: 'center'
                        });
                    }
                }" x-init="$nextTick(() => scrollToPickupTime())"
                    class="w-1/2 text-center lg:text-left overflow-y-scroll h-screen lg:h-auto lg:pt-4">
                    <h3 class="mb-2 font-semibold">{{ __('forms.pickup_time_full') }}</h3>

                    <div class="lg:flex flex-wrap gap-2 justify-start">
                        <template x-for="(key, hour) in hours" :key="key">
                            <div x-text="hour" class="py-1 lg:py-2 lg:px-2 lg:rounded cursor-pointer hover:bg-cta-primary/90 focus:bg-cta-primary/90 "
                                @click="Cookies.set('pickup_time', hour); pickup_time = hour;" :data-pickup-time="hour"
                                :class="pickup_time == hour ? 'bg-eurodefault' : ''">
                            </div>
                        </template>
                    </div>
                </div>


                <div x-data="{
                    scrollToDropoffTime() {
                        const selectedDropoffTime = document.querySelector(`[data-dropoff-time='${this.dropoff_time}']`);
                
                        selectedDropoffTime.scrollIntoView({
                            behavior: 'auto',
                            block: 'center',
                            inline: 'center'
                        });
                    }
                }" x-init="$nextTick(() => scrollToDropoffTime())"
                    class="w-1/2 text-center lg:text-left overflow-y-scroll h-screen lg:h-auto lg:pt-4">
                    <h3 class="mb-2 font-semibold">{{ __('forms.dropoff_time_full') }}</h3>

                    <div class="lg:flex flex-wrap gap-2 justify-start">
                        <template x-for="(key, hour) in hours" :key="key">
                            <div x-text="hour" class="py-1 lg:py-2 lg:px-2 lg:rounded cursor-pointer"
                                @click="Cookies.set('dropoff_time', hour); dropoff_time = hour;"
                                :data-dropoff-time="hour" :class="dropoff_time == hour ? 'bg-eurodefault' : ''"></div>
                        </template>
                    </div>
                </div>


            </div>
        </div>

        <div id="confirm-hours" x-show="timesModal" x-cloak x-show="datesModal"
            class="fixed lg:relative bottom-0 left-0 lg:flex lg:justify-end bg-cta-primary lg:bg-white text-white p-2 lg:mt-4 rounded-lg w-full lg:w-4/6 lg:mx-auto text-xl z-50 text-center cursor-pointer lg:cursor-auto">
            <div class="lg:bg-cta-primary hover:bg-cta-primary/90 focus:bg-cta-primary/90  lg:inline-block lg;py-2 lg:px-4 lg:rounded lg:cursor-pointer"
                @click=" 
                timesModal=false; datesModal=false; 
                document.getElementById('hours-list').style.display = 'none';
                document.getElementById('confirm-hours').style.display = 'none'; ">
                CONFIRM
            </div>
        </div>
    </div>

</div>

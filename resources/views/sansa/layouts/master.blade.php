    <!DOCTYPE html>
    <html lang="{{ LaravelLocalization::getCurrentLocale() }}">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>@yield('title', Lang::get('common.default_title'))</title>
        <meta name="description" content="@yield('meta_description', Lang::get('common.default_meta_description'))">

        <link rel="shortcut icon" type="image/x-icon" href="{!! asset('images/favicon.ico') !!}">
        <meta name="theme-color" content="#fafafa" />

        @yield('head')

        @livewireStyles

        <!-- Scripts -->
        @vite(['resources/css/frontend.css', 'resources/js/frontend.js'])


        @if (!empty(Route::getFacadeRoot()->current()) && isset($enabledLanguages))
            <?php
            $routes = trans('routes');
            $route = array_search(str_replace(LaravelLocalization::getCurrentLocale() . '/', '', Route::getFacadeRoot()->current()->uri()), $routes);
            ?>

            @if (Route::currentRouteName() === 'posts.show')
                @php($currentLocale = App::getLocale())
                @foreach ($enabledLanguages as $enabledLanguage)
                    @if ($post->hasTranslation($enabledLanguage->locale))
                        @php(App::setLocale($enabledLanguage->locale))
                        <link rel="alternate"
                            href="{{ LaravelLocalization::localizeUrl(route('posts.show', $post->slug), $enabledLanguage->locale) }}{!! $url_variables !!}"
                            hreflang="{!! $enabledLanguage->locale !!}">
                    @endif
                @endforeach
                @php(App::setLocale($currentLocale))
            @else
                @foreach ($enabledLanguages as $enabledLanguage)
                    <?php $link = empty($route) ? LaravelLocalization::getLocalizedURL($enabledLanguage->locale) : LaravelLocalization::getURLFromRouteNameTranslated($enabledLanguage->locale, "routes.{$route}", array_filter(Route::getCurrentRoute()->parameters())); ?>
                    <link rel="alternate" href="{!! $link !!}{!! $url_variables !!}"
                        hreflang="{!! $enabledLanguage->locale !!}">
                @endforeach
            @endif
        @endif

        @if (App::environment('production'))
           @include('sansa.layouts.partials.__google')
        @endif

    </head>

    <body class="font-geo-regular text-base max-w-[1920px] block mx-auto">


        @include('sansa.layouts.nav')

        {{ $slot }}


        @livewireScripts
    </body>

    </html>

<div x-data="() => ({
    pickupLocationModal: false,
    dropoffLocationModal: false,
    datesModal: false,
    timesModal: false,
    pickup_location_name: '',
    dropoff_location_name: '',
    differentDropoff: false,
    pickup_date: '',
    pickup_time: '',
    dropoff_date: '',
    dropoff_time: '',
    dates_position_top: 0,
    dates_position_bottom: 0,
    pickup_position_left: 0,
    dropoff_position_right: 0,
    hours: {{ json_encode($time_ranges) }},
    dateFormatted(dateStr) {
        const [day, month, year] = dateStr.split('-');
        const date = new Date(`${year}-${month}-${day}`);
        return new Intl.DateTimeFormat(`{{ app()->getLocale() }}`, {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        }).format(date);
    }
})" {{-- Set default values for pickup and dropoff fields. /// Other settings /// Redraw the calendar --}} x-init="pickup_location_name = '{{ collect($locations_dropdown_grouped)->map(function ($group) {
        return $group[Arr::get($_COOKIE, 'pickup_location')] ?? null;
    })->filter()->first() ?? collect($locations_dropdown_grouped)->first()[1] }}';
dropoff_location_name = '{{ collect($locations_dropdown_grouped)->map(function ($group) {
        return $group[Arr::get($_COOKIE, 'dropoff_location')] ?? null;
    })->filter()->first() ?? collect($locations_dropdown_grouped)->first()[1] }}';
pickup_date = Cookies.get('pickup_date') ?? `{{ Carbon\Carbon::now()->addDays(2)->format('d-m-Y') }}`;
pickup_time = Cookies.get('pickup_time') ?? '12.00';
dropoff_date = Cookies.get('dropoff_date') ?? `{{ Carbon\Carbon::now()->addDays(9)->format('d-m-Y') }}`;
dropoff_time = Cookies.get('dropoff_time') ?? '12.00';
differentDropoff = `{{ Arr::get($_COOKIE, 'dropoff_location') !== Arr::get($_COOKIE, 'pickup_location') }}`;

{{-- Sets the dimensions of the dates overlay when the modal is open in desktop view --}}
$nextTick(() => {
    const ref = document.getElementById('pickup_datetime');
    const ref2 = document.getElementById('dropoff_datetime');
    const overlay = document.getElementById('dates_overlay');

    dates_position_top = ref.offsetTop;
    dates_position_bottom = ref.getBoundingClientRect().bottom + 10 + window.scrollY;
    pickup_position_left = ref.getBoundingClientRect().left + window.scrollX;
    dropoff_position_right = ref2.getBoundingClientRect().right + window.scrollX;
    dates_height = ref.getBoundingClientRect().height;

    overlay.style.top = dates_position_top + 'px';
    overlay.style.left = pickup_position_left + 'px';
    overlay.style.right = dropoff_position_right + 'px';
    overlay.style.width = dropoff_position_right - pickup_position_left + 'px';
    overlay.style.height = dates_height + 'px';
});

{{-- Redraws the calendar after page load --}}
$watch('datesModal', value => {
    if (value) {
        $nextTick(() => {
            if (!window.fpInstance) {
                window.initFlatpickr();
            } else {
                window.fpInstance.redraw();
            }
        });
    }
})"
    class="w-10/12 sm:w-2/3 md:w-1/2 lg:w-11/12 xl:w-4/5 2xl:w-4/6 mx-auto z-10"
    @keyup.window.escape="pickupLocationModal = false; dropoffLocationModal = false; datesModal = false; timesModal = false">

    <form action="{{ route('booking.handle') }}" method="POST">
        @csrf

        <div
            class="bg-sand-500 lg:bg-white border-1 border-white rounded-lg lg:rounded-xl shadow-lg lg:shadow-3xl text-sm font-normal px-5 py-8 flex flex-col lg:flex-row lg:items-start gap-3 lg:gap-1 xl:gap-2 z-20">
            {{-- Pickup Location --}}
            <div :class="differentDropoff ? 'lg:w-1/4' : 'lg:w-2/4'">
                <x-sansa.form.label for="pickup_location">
                    <div class="relative block h-5">
                        <span class="inline-block">{{ __('forms.pickup') }}</span>
                        <span x-show="differentDropoff"
                            x-transition:enter="transition ease-[cubic-bezier(.5,0,1,0)] duration-1000"
                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                            x-transition:leave="transition ease-[cubic-bezier(0,1,0,1] duration-400"
                            x-transition:leave-start="" x-transition:leave-end="opacity-0" x-cloak
                            class="absolute inset-0">{{ __('forms.pickup') }}</span>

                        <span x-show="!differentDropoff"
                            x-transition:enter="transition ease-[cubic-bezier(.5,0,1,0)] duration-1000"
                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                            x-transition:leave="transition ease-[cubic-bezier(0,1,0,1] duration-400"
                            x-transition:leave-start="" x-transition:leave-end="opacity-0" x-cloak class="inline-block">
                            / {{ __('forms.dropoff') }} </span>
                    </div>
                </x-sansa.form.label>

                <div class="relative">
                    <x-sansa.icons.location-icon class="w-6 absolute top-2 left-1" />
                    <x-sansa.form.input @click="pickupLocationModal = true" id="pickup_location" readonly class="pl-8"
                        x-bind:class="pickupLocationModal ?
                            '!shadow-active-input !border-white rounded-bl-none rounded-br-none !pb-4 transition duration-1000 ease-in' :
                            ''"
                        x-model="pickup_location_name" />
                </div>

                {{-- Different Dropoff Checkbox --}}
                <x-sansa.form.checkbox class="mt-2" x-model="differentDropoff"
                    @click="
                                !differentDropoff ? null : Cookies.set('dropoff_location', Cookies.get('pickup_location'));
                                $dispatch('dropoff_state', { state: !differentDropoff });
                            ">
                    {{ __('forms.different_dropoff_location') }}
                </x-sansa.form.checkbox>
            </div>
            {{-- Dropoff Location --}}
            <div class="mb-2 lg:w-1/4" x-show="differentDropoff"
                x-transition:enter="transition ease-[cubic-bezier(.5,0,1,0)] duration-1000"
                x-transition:enter-start="opacity-0 h-0" x-transition:enter-end="opacity-100 h-auto"
                x-transition:leave="transition ease-[cubic-bezier(0,1,0,1] duration-400" x-transition:leave-start=""
                x-transition:leave-end="opacity-0" x-cloak>
                <x-sansa.form.label for="dropoff_location">
                    {{ __('forms.dropoff') }}
                </x-sansa.form.label>

                <div class="relative">
                    <x-sansa.icons.location-icon class="w-6 absolute top-2 left-1" />
                    <x-sansa.form.input @click="dropoffLocationModal = true" id="dropoff_location" readonly
                        class="pl-8"
                        x-bind:class="dropoffLocationModal ?
                            '!shadow-active-input !border-white rounded-bl-none rounded-br-none !pb-4 transition duration-1000 ease-in' :
                            ''"
                        x-model="dropoff_location_name" />
                </div>
            </div>
            {{-- Pickup Date / Time --}}
            <div class="mb-2 lg:w-1/4">
                <x-sansa.form.label for="pickup_date">
                    {{ __('forms.pickup_date_full') }}
                </x-sansa.form.label>
                <div id="pickup_datetime" class="flex relative border border-sand-700 lg:border-eurodefault rounded-lg">
                    <div class="lg:w-3/5">
                        <x-sansa.icons.calendar-icon class="w-6 absolute top-2 left-1" />
                        <x-sansa.form.input @click="datesModal = true" id="pickup_date" x-model="pickup_date"
                            type="text" readonly
                            class="!border-none rounded-none pl-10 lg:pl-8 pr-2 lg:pr-0 rounded-tl-lg rounded-bl-lg" />
                    </div>
                    <div class="lg:w-2/5">
                        <x-sansa.form.input @click="timesModal = true" id="pickup_time" x-model="pickup_time"
                            type="text" readonly
                            class="!border-none rounded-none text-center rounded-tr-lg rounded-br-lg" />
                    </div>
                </div>
            </div>
            {{-- Dropoff Date / Time --}}
            <div class="mb-2 lg:w-1/4">
                <x-sansa.form.label for="dropoff_date">
                    {{ __('forms.dropoff_date_full') }}
                </x-sansa.form.label>
                <div class="flex relative border border-sand-700 lg:border-eurodefault rounded-lg"
                    id="dropoff_datetime">
                    <div class="lg:w-3/5">
                        <x-sansa.icons.calendar-icon class="w-6 absolute top-2 left-1" />
                        <x-sansa.form.input @click="datesModal = true" id="dropoff_date" x-model="dropoff_date"
                            type="text" readonly
                            class="!border-none rounded-none pl-10 lg:pl-8 pr-2 lg:pr-0 rounded-tl-lg rounded-bl-lg" />
                    </div>
                    <div class="lg:w-2/5">
                        <x-sansa.form.input @click="timesModal = true" id="dropoff_time" x-model="dropoff_time"
                            type="text" readonly
                            class="!border-none rounded-none text-center rounded-tr-lg rounded-br-lg" />
                    </div>
                </div>
            </div>

            {{-- Dates overlay when dates modal is open in desktop view --}}
            <div id="dates_overlay" x-show="datesModal || timesModal"
                class="hidden lg:block bg-white border-t border-l border-r border-sand-700 border-b-none rounded-lg rounded-bl-none rounded-br-none shadow-active-input z-30"
                style="position: absolute;" >
                <div class="flex justify-evenly items-center">
                    <div x-text="dateFormatted(pickup_date) + ', ' + pickup_time" class="p-3 "></div>
                    <span class="text-xl">&#10143;</span>
                    <div x-text="dateFormatted(dropoff_date) + ', ' + dropoff_time" class="p-3 "></div>
                </div>
            </div>

            {{-- Submit --}}

            <x-sansa.form.primary-cta>
                {{ __('forms.find_cars') }}
            </x-sansa.form.primary-cta>

            {{-- Modals --}}
            @include('sansa.modals.location-modal', ['action' => 'pickup'])
            @include('sansa.modals.location-modal', ['action' => 'dropoff'])
            @include('sansa.modals.dates-modal')
        </div>
    </form>
</div>

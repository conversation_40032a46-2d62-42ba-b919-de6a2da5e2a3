@if (!empty(Route::getFacadeRoot()->current()) && isset($enabledLanguages))
    <?php
    $routes = trans('routes');
    $route = array_search(str_replace(LaravelLocalization::getCurrentLocale() . '/', '', Route::getFacadeRoot()->current()->uri()), $routes);
    ?>
    @if (Route::currentRouteName() === 'posts.show')
        @php($currentLocale = App::getLocale())
        @foreach ($enabledLanguages as $enabledLanguage)
            @php(App::setLocale($enabledLanguage->locale))
            @if ($post->hasTranslation($enabledLanguage->locale))
                <a href="{{ LaravelLocalization::localizeUrl(route('posts.show', $post->slug), $enabledLanguage->locale) }}"
                    class="">
                    <span class="uppercase">{{ ucfirst($enabledLanguage->locale) }}</span>
                </a>
            @else
                <a href="{{ LaravelLocalization::localizeUrl('/blog', $enabledLanguage->locale) }}" class="">
                    <span>{{ ucfirst($enabledLanguage->locale) }}</span>
                </a>
            @endif
        @endforeach
        @php(App::setLocale($currentLocale))
    @else
        @foreach ($enabledLanguages as $enabledLanguage)
            <?php
            $link = empty($route) ? LaravelLocalization::getLocalizedURL($enabledLanguage->locale) : LaravelLocalization::getURLFromRouteNameTranslated($enabledLanguage->locale, "routes.{$route}", array_filter(Route::getCurrentRoute()->parameters()));
            ?>
            <a href="{!! $link !!}{!! $url_variables !!}" class="p-1 rounded-md {{ $enabledLanguage->locale == LaravelLocalization::getCurrentLocale() ? 'bg-white' : ''  }}">
                <span class="uppercase">{{ ucfirst($enabledLanguage->locale) }}</span>
            </a>
        @endforeach
    @endif
@endif

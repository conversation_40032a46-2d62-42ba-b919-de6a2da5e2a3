<div x-data="{ showMenu: false }">
    <nav class="py-4 lg:pt-4 lg:pb-2 px-4 lg:px-24 flex lg:justify-between lg:gap-12 items-center border-b-2 border-eurogray/30">
        {{-- Logo --}}
        <div class="w-1/2 lg:w-72 flex-none">
            <a href="/"><img src="{{ asset('/sansa/logo.webp') }}" alt="" class="w-full"></a>
        </div>
        {{-- Desktop menu --}}
        <div class="hidden lg:flex grow gap-8 font-geo-light">
            <div>
                <a href="{!! route('booking.show') !!}">
                    {!! trans('common.book_menu_title') !!}
                </a>
            </div>
            <div>
                <a href="{!! route('listings.index') !!}">
                    {{ trans('common.all_cars') }}
                </a>
            </div>
            <div>
                <a href="{!! route('posts.index') !!}">
                    {!! trans('common.blog') !!}
                </a>
            </div>
        </div>
        
        <div class="flex justify-end gap-4 w-1/2 lg:w-auto flex-none">
            {{-- Phone Icon --}}
            <a href="tel:+302810281338"><img src="{{ asset('sansa/phone.svg') }}" alt="" class="w-6"></a>

            {{-- Language Selector --}}
            <div x-data="{ showLanguages: false }" class="relative">
                <img src="{{ asset('sansa/language.svg') }}" alt="" class="w-6 cursor-pointer"
                    @click="showLanguages = !showLanguages">
                <div x-show="showLanguages" x-transition x-cloak class="absolute">
                    <div class="bg-lightgray absolute -top-6 -right-18 flex flex-col items-center w-10 p-1 rounded-md">
                        @include('sansa.layouts.partials.__language-switcher')
                    </div>
                </div>
            </div>

            {{-- 9 Dots Menu --}}
            <div @click="showMenu = !showMenu">
                <img src="{{ asset('sansa/9dots.svg') }}" alt="" class="w-6">

            </div>
        </div>
    </nav>

    @include('sansa.layouts.partials.__main-menu')

</div>

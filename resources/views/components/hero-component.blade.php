@props(['image'])

<section
    {{ $attributes->merge([
        'class' =>
            "bg-linear-to-b from-peach-500 via-peach-500 to-white 
            lg:h-lvh 
            lg:max-h-[680px]
            py-8 
            relative
            lg:flex flex-col justify-center",
    ]) }}>

    

    {{-- Content --}}
    <div class="z-20">
        {{ $slot }}
    </div>
    
    {{-- Background image visible only on desktop --}}
    <div
        class="hidden lg:block absolute inset-0 bg-no-repeat bg-bottom-right z-0"
        style="background-image: url('{{ $image }}'); background-position: 0 -400px;">
    </div>

    {{-- Gradient overlay --}}
    <div class="hidden lg:block absolute z-10 bottom-0 right-0 left-0 h-2/5 bg-linear-to-t from-white to-transparent"></div>
</section>

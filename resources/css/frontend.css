@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: 'Geologica Regular';
    src: url('/resources/fonts/Geologica-Regular.ttf');
}

@font-face {
    font-family: 'Geologica Black';
    src: url('/resources/fonts/Geologica-Black.ttf');
}

@font-face {
    font-family: 'Geologica ExtraLight';
    src: url('/resources/fonts/Geologica-ExtraLight.ttf');
}

@font-face {
    font-family: 'Manrope Regular';
    src: url('/resources/fonts/Manrope-Regular.ttf');
}

@font-face {
    font-family: 'Manrope Bold';
    src: url('/resources/fonts/Manrope-Bold.ttf');
}

@theme {
    --font-geo-regular: 'Geologica Regular', sans-serif;
    --font-geo-black: 'Geologica Black', sans-serif;
    --font-geo-light: 'Geologica ExtraLight', sans-serif;
    --font-manrope: 'Manrope Regular', sans-serif;
    --font-manrope-bold: 'Manrope Bold', sans-serif;

    --text-base: 1.15rem;
    --text-small: .917rem;

    --color-sand-50: #fef5e0;
    --color-sand-100: #fce5b3;
    --color-sand-200: #fbd486;
    --color-sand-300: #f9c259;
    --color-sand-400: #f8b22d;
    --color-sand-500: #f9dca2;
    --color-sand-600: #e7c17d;
    --color-sand-700: #c7a058;
    --color-sand-800: #a68b3b;
    --color-sand-900: #8a7020;

    --color-peach-50: #fef1e6;
    --color-peach-100: #fcd9b9;
    --color-peach-200: #fbc78c;
    --color-peach-300: #fba25f;
    --color-peach-400: #fb8c3a;
    --color-peach-500: #fbeac7;
    --color-peach-600: #f7d29a;
    --color-peach-700: #e8b874;
    --color-peach-800: #d09f4f;
    --color-peach-900: #b68b34;

    --color-eurogray: #30373d;
    --color-lightgray: #f1f1f1;
    --color-mediumgray: #acafb1;
    --color-eurodefault: #FAB01A;
    --color-eurored: #C92434;
    --color-cta-primary: #FAB01A;

    --shadow-active-input: 0px 12px 0px #fff !important;
    --shadow-3xl: 0px 45px 45px rgba(0, 0, 0, 0.6);

}

@layer base {


    h1 {
        @apply text-2xl text-center font-geo-black mb-3;
    }

    h2 {
        @apply text-center font-geo-black;
    }

    p {
        @apply my-2;
    }

    select {
        @apply p-3 rounded-lg border border-sand-700 px-12 py-3 bg-white appearance-none
    }

    .checkbox-checked:before {
        clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0, 43% 62%);
        content: "";
        height: .65em;
        transform: scale(0);
        transform-origin: bottom left;
        transition: transform .12s ease-in-out;
        width: .65em;
    }

    /* Calendar */

    /* General */
    .flatpickr-day.selected,
    .flatpickr-day.startRange,
    .flatpickr-day.endRange,
    .flatpickr-day.selected.inRange,
    .flatpickr-day.startRange.inRange,
    .flatpickr-day.endRange.inRange,
    .flatpickr-day.selected:focus,
    .flatpickr-day.startRange:focus,
    .flatpickr-day.endRange:focus,
    .flatpickr-day.selected:hover,
    .flatpickr-day.startRange:hover,
    .flatpickr-day.endRange:hover,
    .flatpickr-day.selected.prevMonthDay,
    .flatpickr-day.startRange.prevMonthDay,
    .flatpickr-day.endRange.prevMonthDay,
    .flatpickr-day.selected.nextMonthDay,
    .flatpickr-day.startRange.nextMonthDay,
    .flatpickr-day.endRange.nextMonthDay {
        @apply  !bg-eurodefault;
    }

    span.flatpickr-day.startRange,
    span.flatpickr-day.prevMonthDay.startRange,
    span.flatpickr-day.nextMonthDay.startRange,
    span.flatpickr-day.endRange,
    span.flatpickr-day.prevMonthDay.endRange,
    span.flatpickr-day.nextMonthDay.endRange {
        @apply  !border-eurodefault;
    }

    /* Desktop */
    @media only screen and (min-width: 1025px) {
        .flatpickr-calendar {
            box-shadow: unset !important;
        }

        .flatpickr-calendar.inline {
            @apply mx-auto;
        }

        .dayContainer {
            @apply  !px-2;
        }
    }

    /* Mobile */
    @media only screen and (max-width: 1024px) {
        .flatpickr-calendar {
            width: 100% !important;
        }

        .flatpickr-calendar.inline {
            max-height: unset !important;
        }

        .flatpickr-innerContainer,
        .flatpickr-rContainer {
            flex-direction: column !important;
        }

        .flatpickr-weekdays {
            @apply sticky top-[5.3rem] !bg-white z-10;
        }

        .flatpickr-days {
            flex-direction: column;
            width: 100% !important;
            align-items: center !important;
            margin-bottom: 3rem;
        }

        .dayContainer {
            width: 100% !important;
            padding-bottom: 1rem;
        }

        /* REMOVE default month bar */
        .flatpickr-months {
            display: none !important;
        }

        .fp-month-label {
            @apply bg-gray-200 px-2 py-1 my-2 w-full text-eurodefault text-lg;
        }

        /* Hide all weekday rows */
        .flatpickr-weekdays {
            display: none !important;
        }

        /* Custom one at top only */
        .single-weekdays {
            display: flex !important;
            justify-content: space-between;
            padding: 0.5rem 1rem;
            background: #fafafa;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
        }

        .flatpickr-weekdaycontainer {
            display: flex;
            justify-content: space-between;
            width: 100%;
        }

        .flatpickr-weekday {
            flex: 1;
            text-align: center;
            font-size: 0.875rem;
        }
    }


    /****** Swiper ******/
    .swiper-pagination-bullet {
        @apply !bg-eurodefault;
    }

    @media only screen and (max-width: 1024px) {
        .swiper-button-prev {
            display: none !important;
        }

        .swiper-button-next {
            display: none !important;
        }
    }

    @media only screen and (min-width: 1025px) {
        .swiper-button-prev {
            display: block !important;
            @apply !text-eurodefault !left-[10%] after:border after:border-eurodefault after:rounded-full after:w-[40px] after:h-[40px] after:text-center after:block after:!text-[1rem] after:p-3 ;
        }

        .swiper-button-next {
            display: block !important;
            @apply !text-eurodefault !right-[10%] after:border after:border-eurodefault after:rounded-full after:w-[40px] after:h-[40px] after:text-center after:block after:!text-[1rem] after:p-3 ;
        }
    }

}
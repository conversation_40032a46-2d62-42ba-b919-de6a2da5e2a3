// Localisation based on input data-lang attr
var lang = document.getElementById('datepicker').getAttribute('data-lang');
// var lang = document.documentElement.lang;

switch (lang) {
    case 'el':
        lang = 'gr';
        break;
    case 'fr':
        lang = 'fr';
        break;
    case 'it':
        lang = 'it';
        break;
    case 'de':
        lang = 'de';
        break;
    case 'ru':
        lang = 'ru';
        break;

    default:
        lang = 'default';
        break;
}


// Sets the first date depending on cookies or default
function firstDate() {
    let today = new Date().fp_incr(2).getTime(); // Current date + 2 days in milliseconds

    if (Cookies.get('pickup_date')) {
        let pickupDateStr = Cookies.get('pickup_date');
        let [day, month, year] = pickupDateStr.split('-').map(Number); // Extract day, month, and year
        let pickupDate = new Date(year, month - 1, day).getTime(); // Create Date object (month is 0-indexed)

        if (!isNaN(pickupDate)) {
            if (pickupDate > today) {
                return new Date(pickupDate); // Return pickup date if it's after today
            }
        } else {
            console.error("Invalid date format for 'pickup_date' cookie:", pickupDateStr);
        }
    }

    return new Date(today); // Return today + 2 days if pickup date is not set or is before today
}


// Sets the last date depending on cookies or default
function lastDate() {

    if (Cookies.get('pickup_date')) {
        let pickupDateStr = Cookies.get('pickup_date');
        let [day, month, year] = pickupDateStr.split('-').map(Number); // Extract day, month, and year
        let pickupDate = new Date(year, month - 1, day).getTime(); // Create Date object (month is 0-indexed)

        if (Cookies.get('dropoff_date') == undefined) {
            var minDropoffDate = new Date(year, month - 1, day).fp_incr(7).getTime();
        } else {
            var minDropoffDate = new Date(pickupDate).fp_incr(3).getTime();
        }



        if (Cookies.get('dropoff_date')) {
            let dropoffDateStr = Cookies.get('dropoff_date');
            let [day, month, year] = dropoffDateStr.split('-').map(Number);
            let dropoffDate = new Date(year, month - 1, day).getTime();

            if (dropoffDate <= minDropoffDate) {

                return new Date(minDropoffDate);
            }

            return new Date(dropoffDate);
        }

        return new Date(minDropoffDate);
    } else {
        return new Date().fp_incr(9);
    }
}

function monthsNumber() {

    if (screen.width < 1020) {
        return 15;
    } else if (screen.width < 1220) {
        return 3;
    } else {
        return 4;
    }
}


// loads flatpicker after modal opens.
window.initFlatpickr = function () {

    if (window.fpInstance) return; // prevent duplicate init

    window.fpInstance = flatpickr('#datepicker', {
        mode: "range",
        inline: true,
        showMonths: monthsNumber(),
        minDate: new Date().fp_incr(2),
        dateFormat: 'd-m-Y',
        defaultDate: [firstDate(), lastDate()],
        locale: lang,

        // Set the pickup and dropoff input values
        onReady: function (selectedDates, dateStr, instance) {
            customMobileVerticalLayout(selectedDates, dateStr, instance);


            Cookies.set('pickup_date', this.formatDate(firstDate(), "d-m-Y"));
            Cookies.set('dropoff_date', this.formatDate(lastDate(), "d-m-Y"));
        },
        onChange: function (selectedDates, dateStr, instance) {
            customMobileVerticalLayout(selectedDates, dateStr, instance);

            if (this.selectedDates.length < 2) return;

            var start = this.selectedDates[0].getTime();
            var end = this.selectedDates[1].getTime();
            var diffInDays = (end - start) / 86400000;

            if (diffInDays < 3) {
                var minNewDate = this.formatDate(new Date(start + 259200000), "d-m-Y");
                Cookies.set('dropoff_date', minNewDate);
                document.getElementById('dropoff_date').value = minNewDate;
                this.setDate([this.selectedDates[0], minNewDate]);
            }


        },
        onClose: function (selectedDates, dateStr, instance) {
            customMobileVerticalLayout(selectedDates, dateStr, instance);

            if (this.selectedDates.length > 0 && this.selectedDates[0]) {
                var start = this.formatDate(this.selectedDates[0], 'd-m-Y');
                Cookies.set('pickup_date', start);
                document.getElementById('pickup_date').textContent = start;

                var end = this.formatDate(this.selectedDates[1], 'd-m-Y');
                Cookies.set('dropoff_date', end);

                // Update Modal header dates
                document.getElementById('modal-pickup-date').textContent = start;
                document.getElementById('modal-dropoff-date').textContent = end;

                // Update search bucket fields
                document.getElementById('pickup_date').value = start;
                document.getElementById('dropoff_date').value = end;

                // close dates - opens hours
                document.getElementById('dates-calendar').style.display = 'none';
                document.getElementById('hours-list').style.display = 'block';
                document.getElementById('confirm-hours').style.display = 'block';

                var daysInRange = document.getElementsByClassName('inRange');
                var daysLengthTotal = daysInRange.length + 1;

                const totalDaysElement = document.querySelector('.total-rent-days');
                if (totalDaysElement) {
                    totalDaysElement.textContent = daysLengthTotal;
                }
            }
        }

    });

}
const calendar = document.querySelector('.flatpickr-calendar.inline');
const triggers = document.querySelectorAll('#pickup_date, #dropoff_date');


// Change calendar DOM to look different in mobile

function customMobileVerticalLayout(selectedDates, dateStr, instance) {
    if (window.innerWidth > 768) return;

    const calendar = instance.calendarContainer;

    // REMOVE any Flatpickr's original month header (e.g. arrows and titles)
    calendar.querySelectorAll(".flatpickr-months").forEach(el => el.remove());

    // REMOVE all default weekday headers except custom one
    calendar.querySelectorAll(".flatpickr-weekdays:not(.single-weekdays)").forEach(el => el.remove());

    // ADD a single weekday row manually if not already present
    if (!calendar.querySelector(".single-weekdays")) {
        const weekdayContainer = document.createElement("div");
        weekdayContainer.className = "flatpickr-weekdays single-weekdays";

        const weekdayRow = document.createElement("div");
        weekdayRow.className = "flatpickr-weekdaycontainer";

        const localizedWeekdays =
            (instance.l10n && instance.l10n.weekdays && instance.l10n.weekdays.shorthand)
                ? instance.l10n.weekdays.shorthand
                : ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

        for (let i = 0; i < 7; i++) {
            const span = document.createElement("span");
            span.className = "flatpickr-weekday";
            span.textContent = localizedWeekdays[i];
            weekdayRow.appendChild(span);
        }

        weekdayContainer.appendChild(weekdayRow);

        const innerContainer = calendar.querySelector(".flatpickr-innerContainer");
        if (innerContainer) {
            calendar.insertBefore(weekdayContainer, innerContainer);
        }
    }

    // Remove any existing custom month labels to avoid duplicates
    calendar.querySelectorAll(".fp-month-label").forEach(el => el.remove());

    // Add month labels before each dayContainer
    const dayContainers = calendar.querySelectorAll(".dayContainer");
    const baseMonth = instance.currentMonth;
    const baseYear = instance.currentYear;


    dayContainers.forEach((container, index) => {
        const label = document.createElement("div");
        label.className = "fp-month-label";
        const date = new Date(baseYear, baseMonth + index, 1);
        const locale = instance.config?.lang || "default";

        label.textContent = date.toLocaleDateString(locale, {
            month: "long",
            year: "numeric"
        });


        container.parentNode.insertBefore(label, container);
    });
}

import './bootstrap';

import Cookies from 'js-cookie';
import flatpickr from 'flatpickr';
import "flatpickr/dist/flatpickr.min.css";
import "flatpickr/dist/themes/airbnb.css";
import rangePlugin from 'flatpickr/dist/plugins/rangePlugin';
import { German } from 'flatpickr/dist/l10n//de';
import { French } from 'flatpickr/dist/l10n//fr';
import { Greek } from 'flatpickr/dist/l10n//gr';
import { Italian } from 'flatpickr/dist/l10n//it';
import { Russian } from 'flatpickr/dist/l10n//ru';
import { English } from 'flatpickr/dist/l10n//default';
import { range } from "lodash";
import Swiper from 'swiper';
import { Navigation, Pagination, Scrollbar } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';



/**
 * In Vite, import.meta.env is a special object that gives you access to environment variables during development and build.
    import.meta.env.DEV is true when running vite (i.e., development server)
    import.meta.env.PROD is true when running vite build (i.e., production build)
 */

window.Cookies = Cookies;
// Set default expiration to 7 days for all cookies
Cookies.defaults = {
    expires: 14, // Number of days until the cookie expires
    path: '/',
};

// Initialize location cookies
if (!Cookies.get('pickup_location')) {
    Cookies.set('pickup_location', 1);
}
if (!Cookies.get('dropoff_location')) {
    Cookies.set('dropoff_location', 1);
}

// Intialize date cookies
(
    function () {

        // Min pickpu is today + 2 days. Returns a full date object
        let today = new Date().fp_incr(2);

        // Format a date to "d-m-Y"
        function formatDate(d) {
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0'); // JS months are 0-indexed
            const year = d.getFullYear();
            return `${day}-${month}-${year}`;
        }

        // Check if a given date is in the past
        function dateInPast(cookieDate) {

            let [day, month, year] = cookieDate.split('-').map(Number);

            let comparable = new Date(year, month - 1, day).getTime();

            if (!isNaN(comparable)) {
                if (comparable < today.getTime()) {
                    return true; //It is in the past
                } else {
                    return false; //It is not
                }
            }
        }

        // Set pickup date cookie if it is not set or if it is set in the past
        if (!Cookies.get('pickup_date') || dateInPast(Cookies.get('pickup_date'))) {

            let pickupDate = formatDate(today);

            Cookies.set('pickup_date', pickupDate);

        }

        // If dropoff date is not set or is in the past, set it to today +9 days
        if (!Cookies.get('dropoff_date') || dateInPast(Cookies.get('dropoff_date'))) {

            let dropoffDate = new Date().fp_incr(9);

            Cookies.set('dropoff_date', formatDate(dropoffDate));

        } else {
            // Check duration of the bookig, It should be min 3 days

            let [day1, month1, year1] = Cookies.get('pickup_date').split('-').map(Number);
            let pickupDateTime = new Date(year1, month1 - 1, day1).getTime();

            let [day2, month2, year2] = Cookies.get('dropoff_date').split('-').map(Number);
            let dropoffDateTime = new Date(year2, month2 - 1, day2).getTime();


            if (!isNaN(pickupDateTime) && !isNaN(dropoffDateTime)) {

                // Minimum dropoff date is pickup date + 3 days
                let minDropoff = new Date(pickupDateTime).fp_incr(3).getTime();

                if (dropoffDateTime < minDropoff) {
                    let dropoffDate = new Date(minDropoff);

                    Cookies.set('dropoff_date', formatDate(dropoffDate));
                }
            }

        }

    }
)();

window.datesModalComponent = function (initialState) {
    return {
        // initialize from backend
        pickupLocationModal: false,
        dropoffLocationModal: false,
        datesModal: initialState.datesModal ?? false,
        timesModal: initialState.timesModal ?? false,

        pickup_location_name: initialState.pickup_location_name,
        dropoff_location_name: initialState.dropoff_location_name,
        pickup_date: initialState.pickup_date,
        pickup_time: initialState.pickup_time,
        dropoff_date: initialState.dropoff_date,
        dropoff_time: initialState.dropoff_time,
        differentDropoff: initialState.differentDropoff,
        hours: initialState.hours,

        scrollToDefaultPickup() {
            this.$nextTick(() => {
                const container = this.$refs.pickupContainer;
                const defaultPickup = container.querySelector(`[data-pickup-time='${this.pickup_time}']`);
                if (container && defaultPickup) {
                    const offsetTop = defaultPickup.offsetTop;
                    const containerHeight = container.clientHeight;
                    const itemHeight = defaultPickup.offsetHeight;
                    container.scrollTop = offsetTop - (containerHeight / 2) + (itemHeight / 2);
                }
            });
        },

        scrollToDefaultDropoff() {
            this.$nextTick(() => {
                const container = this.$refs.dropoffContainer;
                const defaultDropoff = container.querySelector(`[data-dropoff-time='${this.dropoff_time}']`);
                if (container && defaultDropoff) {
                    const offsetTop = defaultDropoff.offsetTop;
                    const containerHeight = container.clientHeight;
                    const itemHeight = defaultDropoff.offsetHeight;
                    container.scrollTop = offsetTop - (containerHeight / 2) + (itemHeight / 2);
                }
            });
        }
    }
}


// Initialize time cookies
if (!Cookies.get('pickup_time')) {
    Cookies.set('pickup_time', '12:00');
}

if (!Cookies.get('dropoff_time')) {
    Cookies.set('dropoff_time', '12:00');
}

import('./_date_fields');

// Listings slider
window.addEventListener('DOMContentLoaded', () => {

    if (document.querySelector('.listing-swiper')) {
        // console.log(document.querySelector('.listing-swiper'));
        new Swiper('.listing-swiper', {
            modules: [Navigation, Pagination, Scrollbar],
            slidesPerView: 1.2,
            spaceBetween: 20,
            loop: false,
            autoplay: false,
            breakpoints: {
                640: { slidesPerView: 2.2, spaceBetween: 20 },
                768: { slidesPerView: 3.1, spaceBetween: 30 },
                1024: {
                    slidesPerView: 3, spaceBetween: 30,
                    pagination: {
                        el: ".swiper-pagination",
                        dynamicBullets: true,
                    },
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                },
            },
        });
    }
});




# 🧪 Comprehensive Testing Strategy

This document outlines the complete testing strategy for the Laravel Rentals project, including setup, execution, coverage reporting, and best practices.

## 📊 **Current Status**

### ✅ **Achievements**
- **Memory Issues**: ✅ Resolved (2G memory limit, PCOV coverage)
- **SQLite Compatibility**: ✅ Resolved (database-aware migrations)
- **Coverage Infrastructure**: ✅ Complete (HTML, XML, text reports)
- **Test Execution**: ✅ All unit tests passing (33/33)
- **Factory System**: ✅ Complete (Post, PostTranslation, LocalisedMotif)

### 📈 **Test Results**
- **Unit Tests**: 33 passing (106 assertions)
- **Helper Tests**: 5 passing (30 assertions)
- **LocalisedMotif Tests**: 23 passing (65 assertions)
- **Coverage**: Generated successfully with detailed reports

---

## 🏗️ **Testing Infrastructure**

### **1. Environment Setup**

#### **Docker Configuration**
```yaml
# docker-compose.yml
services:
  rentals:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

#### **PHP Testing Configuration**
```ini
# docker/rentals/php-testing.ini
memory_limit = 2G
max_execution_time = 300
xdebug.mode = coverage
xdebug.max_nesting_level = 512
```

#### **Database Configuration**
```php
# .env.testing
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
```

### **2. Coverage Drivers**

#### **Primary: PCOV (Fast)**
- ✅ Installed and configured
- ✅ Faster than Xdebug
- ✅ Production-ready

#### **Fallback: Xdebug**
- ✅ Available as backup
- ✅ Full feature support
- ⚠️ Slower performance

---

## 🛠️ **Testing Tools & Scripts**

### **1. Coverage Scripts**

#### **Main Coverage Script**
```bash
# Generate HTML coverage report
./scripts/test-coverage.sh --html

# Generate all formats
./scripts/test-coverage.sh --all

# Clean coverage files
./scripts/test-coverage.sh --clean
```

#### **Selective Coverage Script**
```bash
# Run coverage on specific test suites
./scripts/selective-coverage.sh unit      # Unit tests only
./scripts/selective-coverage.sh helpers  # Helper tests only
./scripts/selective-coverage.sh all      # All suites (selective)
```

### **2. Composer Scripts**

```json
{
  "scripts": {
    "test": "docker compose exec rentals php artisan test",
    "test-coverage-html": "./scripts/test-coverage.sh --html",
    "test-coverage-clover": "./scripts/test-coverage.sh --clover",
    "coverage-unit": "./scripts/selective-coverage.sh unit",
    "coverage-helpers": "./scripts/selective-coverage.sh helpers"
  }
}
```

### **3. Memory-Optimized Execution**

```bash
# High memory limit for full coverage
docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G artisan test --coverage-html coverage-html

# Selective testing to avoid memory issues
composer coverage-unit
composer coverage-helpers
```

---

## 🏭 **Factory System**

### **1. Post Factory**

#### **Features**
- ✅ Handles translatable attributes
- ✅ Creates PostTranslation records automatically
- ✅ Supports multiple locales (en, el)
- ✅ Configurable states (published, featured)

#### **Usage Examples**
```php
// Basic post with English translation
$post = Post::factory()->create();

// Published post
$post = Post::factory()->published()->create();

// Bilingual post (English + Greek)
$post = Post::factory()->bilingual()->create();

// Custom title
$post = Post::factory()->withTitle('Custom Title')->create();
```

### **2. PostTranslation Factory**

#### **Features**
- ✅ Granular control over translations
- ✅ Locale-specific states
- ✅ Custom title support
- ✅ Automatic slug generation

#### **Usage Examples**
```php
// English translation
$translation = PostTranslation::factory()->english()->create();

// Greek translation for specific post
$translation = PostTranslation::factory()
    ->greek()
    ->forPost($post)
    ->create();
```

### **3. LocalisedMotif Factory**

#### **Features**
- ✅ Location-based data generation
- ✅ Airport and coastal town states
- ✅ Realistic descriptions
- ✅ Varied test data

#### **Usage Examples**
```php
// Basic localised motif
$motif = LocalisedMotif::factory()->create();

// Airport location
$motif = LocalisedMotif::factory()->airport()->create();

// Coastal town
$motif = LocalisedMotif::factory()->coastal()->create();
```

---

## 🗄️ **Database Compatibility**

### **1. Migration Strategy**

#### **Database-Aware Migrations**
```php
// Check database driver
$driver = DB::getDriverName();

if ($driver === 'sqlite') {
    // SQLite-specific logic
    Schema::table('table', function (Blueprint $table) {
        $table->string('column')->nullable();
    });
} else {
    // MySQL-specific logic
    DB::statement("ALTER TABLE table MODIFY COLUMN column ENUM('a','b')");
}
```

#### **Key Fixes Implemented**
- ✅ ENUM compatibility (language field)
- ✅ Multiple column drops (photoable columns)
- ✅ Translatable table structure
- ✅ Foreign key constraints

### **2. SQLite Optimizations**

#### **Column Modifications**
- Made legacy columns nullable for testing
- Separated column drops into individual operations
- Added database driver detection

#### **Table Structure**
```sql
-- Posts table (testing-compatible)
CREATE TABLE posts (
    id INTEGER PRIMARY KEY,
    language VARCHAR(10) DEFAULT 'en',
    title VARCHAR(200) NULL,        -- Nullable for testing
    slug VARCHAR(255) NULL,         -- Nullable for testing
    content TEXT NULL,              -- Nullable for testing
    published BOOLEAN DEFAULT 0,
    featured BOOLEAN DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

---

## 📋 **Test Categories**

### **1. Unit Tests**

#### **Helper Tests** (`tests/Unit/Helpers/`)
- ✅ TestHelper utility functions
- ✅ Currency formatting
- ✅ Email validation
- ✅ String manipulation
- ✅ Array operations

#### **Model Tests** (`tests/Unit/`)
- ✅ LocalisedMotif model (23 tests)
- ✅ Model relationships
- ✅ Factory functionality
- ✅ Database operations

#### **Business Logic Tests**
- ✅ Budget calculations
- ✅ Pricing logic
- ✅ Data transformations

### **2. Feature Tests**

#### **Database Tests** (`tests/Feature/`)
- ✅ Migration compatibility
- ✅ Schema validation
- ✅ Data integrity

#### **Integration Tests**
- 🔄 Controller functionality
- 🔄 API endpoints
- 🔄 Form validation

### **3. Coverage Targets**

| Component | Target | Current | Status |
|-----------|--------|---------|--------|
| Helpers | 95%+ | ✅ High | Complete |
| Models | 90%+ | ✅ High | Complete |
| Controllers | 80%+ | 🔄 TBD | Pending |
| Services | 90%+ | 🔄 TBD | Pending |
| Overall | 80%+ | 🔄 Growing | In Progress |

---

## 🚀 **Execution Workflows**

### **1. Development Workflow**

```bash
# 1. Run quick unit tests
composer test

# 2. Generate coverage for specific area
composer coverage-helpers

# 3. Full unit test coverage
composer coverage-unit

# 4. Check coverage report
open coverage-unit/index.html
```

### **2. CI/CD Workflow**

```bash
# 1. Setup testing environment
docker compose up -d
docker compose exec rentals php artisan migrate --force

# 2. Run all tests with coverage
./scripts/test-coverage.sh --clover

# 3. Upload coverage to CI service
# (coverage.xml generated)
```

### **3. Pre-commit Workflow**

```bash
# 1. Run affected tests
docker compose exec rentals php artisan test tests/Unit/Helpers/

# 2. Quick coverage check
./scripts/selective-coverage.sh helpers

# 3. Verify no regressions
composer test
```

---

## 📊 **Coverage Reporting**

### **1. Report Formats**

#### **HTML Reports** (Interactive)
- 📍 Location: `coverage-html/index.html`
- 🎯 Use: Development, detailed analysis
- 📈 Features: Line-by-line coverage, interactive browsing

#### **Clover XML** (CI/CD)
- 📍 Location: `coverage.xml`
- 🎯 Use: Automated systems, badges
- 📈 Features: Machine-readable, integration-friendly

#### **Text Reports** (Terminal)
- 📍 Output: Console
- 🎯 Use: Quick checks, scripts
- 📈 Features: Summary statistics, fast feedback

### **2. Coverage Analysis**

#### **Key Metrics**
- **Line Coverage**: Percentage of executed lines
- **Function Coverage**: Percentage of called functions
- **Branch Coverage**: Percentage of executed branches
- **Class Coverage**: Percentage of instantiated classes

#### **Quality Gates**
```bash
# Minimum coverage thresholds
MINIMUM_COVERAGE=80
HELPER_COVERAGE=95
MODEL_COVERAGE=90
CONTROLLER_COVERAGE=80
```

---

## 🔧 **Troubleshooting**

### **1. Memory Issues**

#### **Symptoms**
- Tests fail with "memory exhausted"
- Coverage generation stops

#### **Solutions**
```bash
# Increase memory limit
docker compose exec rentals php -d memory_limit=2G artisan test

# Use selective coverage
./scripts/selective-coverage.sh unit

# Check container memory
docker stats
```

### **2. Migration Issues**

#### **Symptoms**
- SQLite constraint violations
- Column modification errors

#### **Solutions**
```bash
# Fresh migration
docker compose exec rentals php artisan migrate:fresh --force

# Check database driver
docker compose exec rentals php artisan tinker
>>> DB::getDriverName()
```

### **3. Factory Issues**

#### **Symptoms**
- NOT NULL constraint failures
- Missing relationships

#### **Solutions**
```php
// Use factories instead of direct creation
$post = Post::factory()->create();  // ✅ Good
$post = Post::create([...]);        // ❌ May fail

// Ensure required relationships
$post = Post::factory()->bilingual()->create();
```

---

## 📈 **Performance Optimization**

### **1. Test Execution Speed**

#### **Strategies**
- Use in-memory SQLite for speed
- Selective test execution
- Parallel test execution (future)

#### **Benchmarks**
- Helper tests: ~1.0s (5 tests)
- Unit tests: ~28s (33 tests)
- LocalisedMotif tests: ~19s (23 tests)

### **2. Coverage Generation Speed**

#### **PCOV vs Xdebug**
- PCOV: ~30% faster
- Xdebug: More features, slower
- Memory usage: Similar

#### **Optimization Tips**
```bash
# Use PCOV when available
docker compose exec rentals php -m | grep pcov

# Selective coverage for development
./scripts/selective-coverage.sh helpers

# Full coverage for CI/CD only
./scripts/test-coverage.sh --all
```

---

## 🎯 **Best Practices**

### **1. Test Writing**

#### **Structure**
```php
class ExampleTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_descriptive_name(): void
    {
        // Arrange
        $data = ['key' => 'value'];
        
        // Act
        $result = SomeClass::process($data);
        
        // Assert
        $this->assertEquals('expected', $result);
    }
}
```

#### **Guidelines**
- ✅ Use descriptive test names
- ✅ Follow Arrange-Act-Assert pattern
- ✅ Test one thing per test
- ✅ Use factories for data creation
- ✅ Mock external dependencies

### **2. Factory Design**

#### **Principles**
- ✅ Provide sensible defaults
- ✅ Support state variations
- ✅ Handle relationships properly
- ✅ Generate realistic data

#### **Example**
```php
class PostFactory extends Factory
{
    public function definition(): array
    {
        return [
            'published' => $this->faker->boolean(70),
            'featured' => $this->faker->boolean(20),
        ];
    }
    
    public function published(): static
    {
        return $this->state(['published' => true]);
    }
}
```

### **3. Coverage Goals**

#### **Priorities**
1. **Critical Business Logic**: 95%+
2. **Models & Relationships**: 90%+
3. **Controllers & APIs**: 80%+
4. **Helpers & Utilities**: 95%+
5. **Views & Presentation**: 60%+

#### **Focus Areas**
- Payment processing
- Reservation logic
- Pricing calculations
- Data validation
- Security features

---

## 🔮 **Future Enhancements**

### **1. Test Infrastructure**

#### **Planned Improvements**
- [ ] Parallel test execution
- [ ] Browser testing (Dusk)
- [ ] API testing suite
- [ ] Performance testing
- [ ] Load testing

#### **CI/CD Integration**
- [ ] GitHub Actions workflow
- [ ] Automated coverage reporting
- [ ] Quality gates
- [ ] Deployment testing

### **2. Coverage Expansion**

#### **Target Areas**
- [ ] Controller tests (80% coverage)
- [ ] Service layer tests (90% coverage)
- [ ] API endpoint tests (85% coverage)
- [ ] Form validation tests (95% coverage)
- [ ] Security tests (90% coverage)

#### **Advanced Features**
- [ ] Mutation testing
- [ ] Property-based testing
- [ ] Contract testing
- [ ] Visual regression testing

### **3. Monitoring & Analytics**

#### **Metrics Tracking**
- [ ] Coverage trends over time
- [ ] Test execution performance
- [ ] Flaky test detection
- [ ] Code quality metrics

#### **Reporting**
- [ ] Coverage badges
- [ ] Trend analysis
- [ ] Team dashboards
- [ ] Automated alerts

---

## 📚 **Resources & Documentation**

### **1. Quick Reference**

#### **Common Commands**
```bash
# Run all tests
composer test

# Generate HTML coverage
composer test-coverage-html

# Run specific test file
docker compose exec rentals php artisan test tests/Unit/LocalisedMotifTest.php

# Run specific test method
docker compose exec rentals php artisan test --filter="test_method_name"

# Clean coverage files
./scripts/test-coverage.sh --clean
```

#### **File Locations**
- Tests: `tests/`
- Factories: `database/factories/`
- Coverage: `coverage-*/`
- Scripts: `scripts/`
- Documentation: `docs/`

### **2. External Resources**

#### **Laravel Testing**
- [Laravel Testing Documentation](https://laravel.com/docs/testing)
- [PHPUnit Documentation](https://phpunit.de/documentation.html)
- [Factory Documentation](https://laravel.com/docs/database-testing#factories)

#### **Coverage Tools**
- [PCOV Documentation](https://github.com/krakjoe/pcov)
- [Xdebug Documentation](https://xdebug.org/docs/code_coverage)

---

## ✅ **Checklist for New Features**

### **Before Development**
- [ ] Plan test strategy
- [ ] Identify coverage targets
- [ ] Design factory requirements
- [ ] Consider edge cases

### **During Development**
- [ ] Write tests first (TDD)
- [ ] Maintain coverage above 80%
- [ ] Test happy and error paths
- [ ] Validate with real data

### **Before Deployment**
- [ ] All tests passing
- [ ] Coverage targets met
- [ ] Performance acceptable
- [ ] Documentation updated

---

*This testing strategy is a living document that evolves with the project. Regular updates ensure it remains current and useful for the development team.*

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Testing Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options specific to the testing environment
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Skip Migrations
    |--------------------------------------------------------------------------
    |
    | List of migration files to skip during testing.
    | Useful for migrations that use database-specific features.
    |
    */
    'skip_migrations' => [
        // Add migration filenames here to skip them during testing
        // Example: '2023_05_31_095738_update_language_field_in_posts_table.php'
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Compatibility
    |--------------------------------------------------------------------------
    |
    | Settings for handling database compatibility issues in tests
    |
    */
    'database' => [
        'sqlite_compatibility_mode' => true,
        'skip_enum_migrations' => true,
        'skip_foreign_key_checks' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Coverage Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for test coverage reporting
    |
    */
    'coverage' => [
        'memory_limit' => '2G',
        'max_execution_time' => 600,
        'driver' => 'pcov', // pcov, xdebug, or auto
    ],
];

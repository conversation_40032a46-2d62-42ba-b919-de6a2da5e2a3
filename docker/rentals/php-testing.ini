[PHP]
; PHP configuration for testing and coverage

; Memory settings
memory_limit = 2G
max_execution_time = 300
max_input_time = 300

; Xdebug settings for coverage
xdebug.mode = coverage
xdebug.max_nesting_level = 512

; Error reporting
display_errors = On
display_startup_errors = On
log_errors = On
error_reporting = E_ALL

; Performance settings for testing
opcache.enable = 0
opcache.enable_cli = 0

; File upload settings (for testing)
upload_max_filesize = 100M
post_max_size = 100M

; Session settings
session.gc_maxlifetime = 7200

; Other useful settings for testing
default_socket_timeout = 300
auto_detect_line_endings = On

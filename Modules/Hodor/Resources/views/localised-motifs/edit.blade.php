@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Localised Motif Details</h3>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">ID</dt>
                    <dd class="col-sm-8">{{ $localised_motif->id }}</dd>
                    
                    <dt class="col-sm-4">Title</dt>
                    <dd class="col-sm-8">{{ $localised_motif->title }}</dd>
                    
                    <dt class="col-sm-4">Description</dt>
                    <dd class="col-sm-8">{{ $localised_motif->description }}</dd>
                    
                    <dt class="col-sm-4">Posts</dt>
                    <dd class="col-sm-8">
                        @if($localised_motif->posts()->exists())
                            {{ $localised_motif->posts()->count() }} total ({{ $localised_motif->publishedPosts()->count() }} published)
                        @else
                            No posts associated
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Created</dt>
                    <dd class="col-sm-8">{{ $localised_motif->created_at->format('Y-m-d H:i:s') }}</dd>
                    
                    <dt class="col-sm-4">Updated</dt>
                    <dd class="col-sm-8">{{ $localised_motif->updated_at->format('Y-m-d H:i:s') }}</dd>
                </dl>
            </div>
        </div>
        <div class="card card-default">
            @include('hodor::common.alert')
            {!! Form::model($localised_motif, array('method' => 'PUT', 'class' => 'main', 'route' => array('hodor.localised-motifs.update', $localised_motif->id))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('title', 'Title*') !!}
                                {!! Form::text('title', null, ['class' => 'form-control' . ($errors->has('title') ? ' is-invalid' : null)]) !!}
                                @if ($errors->has('title'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('title') }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                {!! Form::label('description', 'Description*') !!}
                                {!! Form::textarea('description', null, ['class' => 'form-control' . ($errors->has('description') ? ' is-invalid' : null), 'rows' => 4]) !!}
                                @if ($errors->has('description'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('description') }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Update</button>
                    <a href="{{ route('hodor.localised-motifs.index') }}" class="btn btn-secondary ml-2">Back to List</a>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection

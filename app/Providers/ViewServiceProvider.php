<?php

namespace App\Providers;

use App\View\Composers\BlogDetailsComposer;
use App\View\Composers\FaqPartialComposer;
use App\View\Composers\SearchBucketComposer;
use App\View\Composers\TestimonialPartialComposer;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Using class based composers...
        View::composer('frontend.partials._testimonials', TestimonialPartialComposer::class);
        View::composer('frontend.partials._faq', FaqPartialComposer::class);
        View::composer(
            [
                'frontend.listings.partials._search_form_homepage',
                'frontend.bookings.partials._search_form_landing',
                'frontend.posts.partials._search_form_blog',
                'frontend.posts.partials._search_form_blog_top',
                '*'
            ],
    SearchBucketComposer::class
        );
        View::composer('frontend.posts.show', BlogDetailsComposer::class);
    }
}

<?php

namespace App\Traits\Relationships;

use App\Post;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait LocalisedMotifRelationships
{

    /**
     * The posts that belong to the localised motif.
     */
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class)
            ->withTimestamps();
    }

    /**
     * The published posts that belong to the localised motif.
     */
    public function publishedPosts(): BelongsToMany
    {
        return $this->posts()
            ->published();
    }
}

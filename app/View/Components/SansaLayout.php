<?php

namespace App\View\Components;

use Closure;
use App\Language;
use Illuminate\View\Component;
use Illuminate\Contracts\View\View;

class SansaLayout extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        $enabledLanguages = Language::where('enabled', '=', true)->get();
        $url_variables = '';
        
        return view('sansa.layouts.master', compact('enabledLanguages', 'url_variables'));
    }
}

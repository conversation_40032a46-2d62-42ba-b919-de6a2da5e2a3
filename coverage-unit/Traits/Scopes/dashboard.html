<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Traits/Scopes</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Traits</a></li>
         <li class="breadcrumb-item"><a href="index.html">Scopes</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GoogleReviewScopes.php.html#5">App\Traits\Scopes\GoogleReviewScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingScopes.php.html#5">App\Traits\Scopes\ListingScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#5">App\Traits\Scopes\LocalisableScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationScopes.php.html#5">App\Traits\Scopes\ReservationScopes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostScopes.php.html#5">App\Traits\Scopes\PostScopes</a></td><td class="text-right">50%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GoogleReviewScopes.php.html#13"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopePublished">scopePublished</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewScopes.php.html#24"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeTestimonialised">scopeTestimonialised</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewScopes.php.html#35"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeShowable">scopeShowable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GoogleReviewScopes.php.html#53"><abbr title="App\Traits\Scopes\GoogleReviewScopes::scopeTestimoniable">scopeTestimoniable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingScopes.php.html#13"><abbr title="App\Traits\Scopes\ListingScopes::scopePublishedEurodollar">scopePublishedEurodollar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListingScopes.php.html#24"><abbr title="App\Traits\Scopes\ListingScopes::scopePublishedCretanrentals">scopePublishedCretanrentals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#14"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeEl">scopeEl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#25"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeEn">scopeEn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#36"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeDe">scopeDe</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#47"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeFr">scopeFr</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LocalisableScopes.php.html#58"><abbr title="App\Traits\Scopes\LocalisableScopes::scopeIt">scopeIt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PostScopes.php.html#24"><abbr title="App\Traits\Scopes\PostScopes::scopeFeatured">scopeFeatured</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReservationScopes.php.html#13"><abbr title="App\Traits\Scopes\ReservationScopes::scopeOnlyShow">scopeOnlyShow</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 13:50:57 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,1,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"GoogleReviewScopes.php.html#5\">App\\Traits\\Scopes\\GoogleReviewScopes<\/a>"],[0,2,"<a href=\"ListingScopes.php.html#5\">App\\Traits\\Scopes\\ListingScopes<\/a>"],[0,5,"<a href=\"LocalisableScopes.php.html#5\">App\\Traits\\Scopes\\LocalisableScopes<\/a>"],[50,2,"<a href=\"PostScopes.php.html#5\">App\\Traits\\Scopes\\PostScopes<\/a>"],[0,1,"<a href=\"ReservationScopes.php.html#5\">App\\Traits\\Scopes\\ReservationScopes<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"GoogleReviewScopes.php.html#13\">App\\Traits\\Scopes\\GoogleReviewScopes::scopePublished<\/a>"],[0,1,"<a href=\"GoogleReviewScopes.php.html#24\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeTestimonialised<\/a>"],[0,1,"<a href=\"GoogleReviewScopes.php.html#35\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeShowable<\/a>"],[0,1,"<a href=\"GoogleReviewScopes.php.html#53\">App\\Traits\\Scopes\\GoogleReviewScopes::scopeTestimoniable<\/a>"],[0,1,"<a href=\"ListingScopes.php.html#13\">App\\Traits\\Scopes\\ListingScopes::scopePublishedEurodollar<\/a>"],[0,1,"<a href=\"ListingScopes.php.html#24\">App\\Traits\\Scopes\\ListingScopes::scopePublishedCretanrentals<\/a>"],[0,1,"<a href=\"LocalisableScopes.php.html#14\">App\\Traits\\Scopes\\LocalisableScopes::scopeEl<\/a>"],[0,1,"<a href=\"LocalisableScopes.php.html#25\">App\\Traits\\Scopes\\LocalisableScopes::scopeEn<\/a>"],[0,1,"<a href=\"LocalisableScopes.php.html#36\">App\\Traits\\Scopes\\LocalisableScopes::scopeDe<\/a>"],[0,1,"<a href=\"LocalisableScopes.php.html#47\">App\\Traits\\Scopes\\LocalisableScopes::scopeFr<\/a>"],[0,1,"<a href=\"LocalisableScopes.php.html#58\">App\\Traits\\Scopes\\LocalisableScopes::scopeIt<\/a>"],[100,1,"<a href=\"PostScopes.php.html#13\">App\\Traits\\Scopes\\PostScopes::scopePublished<\/a>"],[0,1,"<a href=\"PostScopes.php.html#24\">App\\Traits\\Scopes\\PostScopes::scopeFeatured<\/a>"],[0,1,"<a href=\"ReservationScopes.php.html#13\">App\\Traits\\Scopes\\ReservationScopes::scopeOnlyShow<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

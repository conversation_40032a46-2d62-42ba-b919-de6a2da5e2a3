<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/html/app/Traits</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item active">Traits</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.25" aria-valuemin="0" aria-valuemax="100" style="width: 2.25%">
           <span class="sr-only">2.25% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.25%</div></td>
       <td class="danger small"><div align="right">7&nbsp;/&nbsp;311</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="3.25" aria-valuemin="0" aria-valuemax="100" style="width: 3.25%">
           <span class="sr-only">3.25% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">3.25%</div></td>
       <td class="danger small"><div align="right">4&nbsp;/&nbsp;123</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.78" aria-valuemin="0" aria-valuemax="100" style="width: 2.78%">
           <span class="sr-only">2.78% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.78%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;36</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-directory.svg" class="octicon" /><a href="AccessorsMutators/index.html">AccessorsMutators</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;138</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;50</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;12</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-directory.svg" class="octicon" /><a href="Relationships/index.html">Relationships</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="4.14" aria-valuemin="0" aria-valuemax="100" style="width: 4.14%">
           <span class="sr-only">4.14% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">4.14%</div></td>
       <td class="danger small"><div align="right">6&nbsp;/&nbsp;145</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="5.08" aria-valuemin="0" aria-valuemax="100" style="width: 5.08%">
           <span class="sr-only">5.08% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">5.08%</div></td>
       <td class="danger small"><div align="right">3&nbsp;/&nbsp;59</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="5.26" aria-valuemin="0" aria-valuemax="100" style="width: 5.26%">
           <span class="sr-only">5.26% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">5.26%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;19</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-directory.svg" class="octicon" /><a href="Scopes/index.html">Scopes</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="3.57" aria-valuemin="0" aria-valuemax="100" style="width: 3.57%">
           <span class="sr-only">3.57% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">3.57%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;28</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="7.14" aria-valuemin="0" aria-valuemax="100" style="width: 7.14%">
           <span class="sr-only">7.14% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">7.14%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;14</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 50%</span>
     <span class="warning"><strong>Medium</strong>: 50% to 80%</span>
     <span class="success"><strong>High</strong>: 80% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 13:50:57 EEST 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>

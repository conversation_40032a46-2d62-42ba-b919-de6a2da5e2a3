<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Providers</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Providers</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BroadcastServiceProvider.php.html#8">App\Providers\BroadcastServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BusServiceProvider.php.html#6">App\Providers\BusServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigServiceProvider.php.html#5">App\Providers\ConfigServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthServiceProvider.php.html#8">App\Providers\AuthServiceProvider</a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#9">App\Providers\AppServiceProvider</a></td><td class="text-right">72%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BroadcastServiceProvider.php.html#13"><abbr title="App\Providers\BroadcastServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BusServiceProvider.php.html#14"><abbr title="App\Providers\BusServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BusServiceProvider.php.html#29"><abbr title="App\Providers\BusServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigServiceProvider.php.html#16"><abbr title="App\Providers\ConfigServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthServiceProvider.php.html#22"><abbr title="App\Providers\AuthServiceProvider::boot">boot</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#22"><abbr title="App\Providers\AppServiceProvider::boot">boot</abbr></a></td><td class="text-right">70%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 13:50:57 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,1,1,0,0,3], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,0,1,1,0,0,7], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[72.72727272727273,2,"<a href=\"AppServiceProvider.php.html#9\">App\\Providers\\AppServiceProvider<\/a>"],[66.66666666666666,1,"<a href=\"AuthServiceProvider.php.html#8\">App\\Providers\\AuthServiceProvider<\/a>"],[0,1,"<a href=\"BroadcastServiceProvider.php.html#8\">App\\Providers\\BroadcastServiceProvider<\/a>"],[0,2,"<a href=\"BusServiceProvider.php.html#6\">App\\Providers\\BusServiceProvider<\/a>"],[0,1,"<a href=\"ConfigServiceProvider.php.html#5\">App\\Providers\\ConfigServiceProvider<\/a>"],[100,2,"<a href=\"EventServiceProvider.php.html#10\">App\\Providers\\EventServiceProvider<\/a>"],[100,2,"<a href=\"RepositoryServiceProvider.php.html#5\">App\\Providers\\RepositoryServiceProvider<\/a>"],[100,2,"<a href=\"ViewServiceProvider.php.html#12\">App\\Providers\\ViewServiceProvider<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"AppServiceProvider.php.html#14\">App\\Providers\\AppServiceProvider::register<\/a>"],[70,1,"<a href=\"AppServiceProvider.php.html#22\">App\\Providers\\AppServiceProvider::boot<\/a>"],[66.66666666666666,1,"<a href=\"AuthServiceProvider.php.html#22\">App\\Providers\\AuthServiceProvider::boot<\/a>"],[0,1,"<a href=\"BroadcastServiceProvider.php.html#13\">App\\Providers\\BroadcastServiceProvider::boot<\/a>"],[0,1,"<a href=\"BusServiceProvider.php.html#14\">App\\Providers\\BusServiceProvider::boot<\/a>"],[0,1,"<a href=\"BusServiceProvider.php.html#29\">App\\Providers\\BusServiceProvider::register<\/a>"],[0,1,"<a href=\"ConfigServiceProvider.php.html#16\">App\\Providers\\ConfigServiceProvider::register<\/a>"],[100,1,"<a href=\"EventServiceProvider.php.html#26\">App\\Providers\\EventServiceProvider::boot<\/a>"],[100,1,"<a href=\"EventServiceProvider.php.html#34\">App\\Providers\\EventServiceProvider::shouldDiscoverEvents<\/a>"],[100,1,"<a href=\"RepositoryServiceProvider.php.html#12\">App\\Providers\\RepositoryServiceProvider::boot<\/a>"],[100,1,"<a href=\"RepositoryServiceProvider.php.html#22\">App\\Providers\\RepositoryServiceProvider::register<\/a>"],[100,1,"<a href=\"ViewServiceProvider.php.html#19\">App\\Providers\\ViewServiceProvider::register<\/a>"],[100,1,"<a href=\"ViewServiceProvider.php.html#29\">App\\Providers\\ViewServiceProvider::boot<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

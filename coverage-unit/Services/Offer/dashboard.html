<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/html/app/Services/Offer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.5" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/html/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Offer</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicDiscount.php.html#16">App\Services\Offer\BasicDiscount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponDiscount.php.html#14">App\Services\Offer\CouponDiscount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#16">App\Services\Offer\DateRange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PredefinedOffers.php.html#18">App\Services\Offer\PredefinedOffers</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#20">App\Services\Offer\RentalOffer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaticDiscount.php.html#16">App\Services\Offer\StaticDiscount</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RentalOffer.php.html#20">App\Services\Offer\RentalOffer</a></td><td class="text-right">3782</td></tr>
       <tr><td><a href="DateRange.php.html#16">App\Services\Offer\DateRange</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="BasicDiscount.php.html#16">App\Services\Offer\BasicDiscount</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="StaticDiscount.php.html#16">App\Services\Offer\StaticDiscount</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="PredefinedOffers.php.html#18">App\Services\Offer\PredefinedOffers</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="CouponDiscount.php.html#14">App\Services\Offer\CouponDiscount</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicDiscount.php.html#59"><abbr title="App\Services\Offer\BasicDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicDiscount.php.html#71"><abbr title="App\Services\Offer\BasicDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicDiscount.php.html#105"><abbr title="App\Services\Offer\BasicDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicDiscount.php.html#129"><abbr title="App\Services\Offer\BasicDiscount::getPeriodPrice">getPeriodPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponDiscount.php.html#22"><abbr title="App\Services\Offer\CouponDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponDiscount.php.html#29"><abbr title="App\Services\Offer\CouponDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#60"><abbr title="App\Services\Offer\DateRange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#76"><abbr title="App\Services\Offer\DateRange::setPeriodCounters">setPeriodCounters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#133"><abbr title="App\Services\Offer\DateRange::getPeriodCounters">getPeriodCounters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#143"><abbr title="App\Services\Offer\DateRange::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#158"><abbr title="App\Services\Offer\DateRange::getExtraDayCharge">getExtraDayCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#166"><abbr title="App\Services\Offer\DateRange::getPickupDateTime">getPickupDateTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#176"><abbr title="App\Services\Offer\DateRange::getPeriod">getPeriod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRange.php.html#223"><abbr title="App\Services\Offer\DateRange::getDaysIndex">getDaysIndex</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PredefinedOffers.php.html#22"><abbr title="App\Services\Offer\PredefinedOffers::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PredefinedOffers.php.html#32"><abbr title="App\Services\Offer\PredefinedOffers::getPredefinedOffers">getPredefinedOffers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#80"><abbr title="App\Services\Offer\RentalOffer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#106"><abbr title="App\Services\Offer\RentalOffer::getDiscountPercentage">getDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#111"><abbr title="App\Services\Offer\RentalOffer::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#122"><abbr title="App\Services\Offer\RentalOffer::getTotalDiscountPrice">getTotalDiscountPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#140"><abbr title="App\Services\Offer\RentalOffer::getRangePrice">getRangePrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#177"><abbr title="App\Services\Offer\RentalOffer::getBasePrice">getBasePrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#187"><abbr title="App\Services\Offer\RentalOffer::getAccessoriesPrice">getAccessoriesPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#208"><abbr title="App\Services\Offer\RentalOffer::getAccessoryPrice">getAccessoryPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#226"><abbr title="App\Services\Offer\RentalOffer::hasExtraDayCharge">hasExtraDayCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#235"><abbr title="App\Services\Offer\RentalOffer::hasExtraMilesCharge">hasExtraMilesCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#250"><abbr title="App\Services\Offer\RentalOffer::getExtraMilesCharge">getExtraMilesCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#259"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationPickupCharge">hasRemoteLocationPickupCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#277"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationDropoffCharge">hasRemoteLocationDropoffCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#291"><abbr title="App\Services\Offer\RentalOffer::getRemoteLocationCharge">getRemoteLocationCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#332"><abbr title="App\Services\Offer\RentalOffer::hasAfterHoursCharge">hasAfterHoursCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#368"><abbr title="App\Services\Offer\RentalOffer::getAfterHoursCharge">getAfterHoursCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#377"><abbr title="App\Services\Offer\RentalOffer::hasFuelPlanCharge">hasFuelPlanCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#382"><abbr title="App\Services\Offer\RentalOffer::getFuelPlanCharge">getFuelPlanCharge</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#390"><abbr title="App\Services\Offer\RentalOffer::hasAvailability">hasAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#398"><abbr title="App\Services\Offer\RentalOffer::setAvailability">setAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#439"><abbr title="App\Services\Offer\RentalOffer::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#450"><abbr title="App\Services\Offer\RentalOffer::getPickupLocation">getPickupLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RentalOffer.php.html#460"><abbr title="App\Services\Offer\RentalOffer::getDropoffLocation">getDropoffLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaticDiscount.php.html#66"><abbr title="App\Services\Offer\StaticDiscount::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaticDiscount.php.html#78"><abbr title="App\Services\Offer\StaticDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaticDiscount.php.html#109"><abbr title="App\Services\Offer\StaticDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaticDiscount.php.html#135"><abbr title="App\Services\Offer\StaticDiscount::calculateTotalPrice">calculateTotalPrice</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PredefinedOffers.php.html#32"><abbr title="App\Services\Offer\PredefinedOffers::getPredefinedOffers">getPredefinedOffers</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="RentalOffer.php.html#140"><abbr title="App\Services\Offer\RentalOffer::getRangePrice">getRangePrice</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DateRange.php.html#76"><abbr title="App\Services\Offer\DateRange::setPeriodCounters">setPeriodCounters</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RentalOffer.php.html#291"><abbr title="App\Services\Offer\RentalOffer::getRemoteLocationCharge">getRemoteLocationCharge</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DateRange.php.html#176"><abbr title="App\Services\Offer\DateRange::getPeriod">getPeriod</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="RentalOffer.php.html#398"><abbr title="App\Services\Offer\RentalOffer::setAvailability">setAvailability</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BasicDiscount.php.html#129"><abbr title="App\Services\Offer\BasicDiscount::getPeriodPrice">getPeriodPrice</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="StaticDiscount.php.html#135"><abbr title="App\Services\Offer\StaticDiscount::calculateTotalPrice">calculateTotalPrice</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BasicDiscount.php.html#71"><abbr title="App\Services\Offer\BasicDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RentalOffer.php.html#235"><abbr title="App\Services\Offer\RentalOffer::hasExtraMilesCharge">hasExtraMilesCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RentalOffer.php.html#259"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationPickupCharge">hasRemoteLocationPickupCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RentalOffer.php.html#277"><abbr title="App\Services\Offer\RentalOffer::hasRemoteLocationDropoffCharge">hasRemoteLocationDropoffCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RentalOffer.php.html#332"><abbr title="App\Services\Offer\RentalOffer::hasAfterHoursCharge">hasAfterHoursCharge</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BasicDiscount.php.html#105"><abbr title="App\Services\Offer\BasicDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CouponDiscount.php.html#29"><abbr title="App\Services\Offer\CouponDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StaticDiscount.php.html#78"><abbr title="App\Services\Offer\StaticDiscount::getDiscountAmount">getDiscountAmount</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StaticDiscount.php.html#109"><abbr title="App\Services\Offer\StaticDiscount::getNumberOfDaysDiscountPercentage">getNumberOfDaysDiscountPercentage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DateRange.php.html#143"><abbr title="App\Services\Offer\DateRange::getTotalDays">getTotalDays</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DateRange.php.html#158"><abbr title="App\Services\Offer\DateRange::getExtraDayCharge">getExtraDayCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#122"><abbr title="App\Services\Offer\RentalOffer::getTotalDiscountPrice">getTotalDiscountPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#177"><abbr title="App\Services\Offer\RentalOffer::getBasePrice">getBasePrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#187"><abbr title="App\Services\Offer\RentalOffer::getAccessoriesPrice">getAccessoriesPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#208"><abbr title="App\Services\Offer\RentalOffer::getAccessoryPrice">getAccessoryPrice</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#250"><abbr title="App\Services\Offer\RentalOffer::getExtraMilesCharge">getExtraMilesCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#368"><abbr title="App\Services\Offer\RentalOffer::getAfterHoursCharge">getAfterHoursCharge</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RentalOffer.php.html#382"><abbr title="App\Services\Offer\RentalOffer::getFuelPlanCharge">getFuelPlanCharge</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.5</a> using <a href="https://www.php.net/" target="_top">PHP 8.1.33</a> and <a href="https://phpunit.de/">PHPUnit 10.3.5</a> at Fri Jul 11 13:50:57 EEST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.5" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([43,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,13,"<a href=\"BasicDiscount.php.html#16\">App\\Services\\Offer\\BasicDiscount<\/a>"],[0,4,"<a href=\"CouponDiscount.php.html#14\">App\\Services\\Offer\\CouponDiscount<\/a>"],[0,21,"<a href=\"DateRange.php.html#16\">App\\Services\\Offer\\DateRange<\/a>"],[100,0,"<a href=\"DiscountBehaviour.php.html#12\">App\\Services\\Offer\\DiscountBehaviour<\/a>"],[0,10,"<a href=\"PredefinedOffers.php.html#18\">App\\Services\\Offer\\PredefinedOffers<\/a>"],[0,61,"<a href=\"RentalOffer.php.html#20\">App\\Services\\Offer\\RentalOffer<\/a>"],[0,12,"<a href=\"StaticDiscount.php.html#16\">App\\Services\\Offer\\StaticDiscount<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BasicDiscount.php.html#59\">App\\Services\\Offer\\BasicDiscount::__construct<\/a>"],[0,4,"<a href=\"BasicDiscount.php.html#71\">App\\Services\\Offer\\BasicDiscount::getDiscountAmount<\/a>"],[0,3,"<a href=\"BasicDiscount.php.html#105\">App\\Services\\Offer\\BasicDiscount::getNumberOfDaysDiscountPercentage<\/a>"],[0,5,"<a href=\"BasicDiscount.php.html#129\">App\\Services\\Offer\\BasicDiscount::getPeriodPrice<\/a>"],[0,1,"<a href=\"CouponDiscount.php.html#22\">App\\Services\\Offer\\CouponDiscount::__construct<\/a>"],[0,3,"<a href=\"CouponDiscount.php.html#29\">App\\Services\\Offer\\CouponDiscount::getDiscountAmount<\/a>"],[0,1,"<a href=\"DateRange.php.html#60\">App\\Services\\Offer\\DateRange::__construct<\/a>"],[0,7,"<a href=\"DateRange.php.html#76\">App\\Services\\Offer\\DateRange::setPeriodCounters<\/a>"],[0,1,"<a href=\"DateRange.php.html#133\">App\\Services\\Offer\\DateRange::getPeriodCounters<\/a>"],[0,2,"<a href=\"DateRange.php.html#143\">App\\Services\\Offer\\DateRange::getTotalDays<\/a>"],[0,2,"<a href=\"DateRange.php.html#158\">App\\Services\\Offer\\DateRange::getExtraDayCharge<\/a>"],[0,1,"<a href=\"DateRange.php.html#166\">App\\Services\\Offer\\DateRange::getPickupDateTime<\/a>"],[0,6,"<a href=\"DateRange.php.html#176\">App\\Services\\Offer\\DateRange::getPeriod<\/a>"],[0,1,"<a href=\"DateRange.php.html#223\">App\\Services\\Offer\\DateRange::getDaysIndex<\/a>"],[100,0,"<a href=\"DiscountBehaviour.php.html#19\">App\\Services\\Offer\\DiscountBehaviour::getDiscountAmount<\/a>"],[0,1,"<a href=\"PredefinedOffers.php.html#22\">App\\Services\\Offer\\PredefinedOffers::__construct<\/a>"],[0,9,"<a href=\"PredefinedOffers.php.html#32\">App\\Services\\Offer\\PredefinedOffers::getPredefinedOffers<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#80\">App\\Services\\Offer\\RentalOffer::__construct<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#106\">App\\Services\\Offer\\RentalOffer::getDiscountPercentage<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#111\">App\\Services\\Offer\\RentalOffer::getDiscountAmount<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#122\">App\\Services\\Offer\\RentalOffer::getTotalDiscountPrice<\/a>"],[0,9,"<a href=\"RentalOffer.php.html#140\">App\\Services\\Offer\\RentalOffer::getRangePrice<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#177\">App\\Services\\Offer\\RentalOffer::getBasePrice<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#187\">App\\Services\\Offer\\RentalOffer::getAccessoriesPrice<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#208\">App\\Services\\Offer\\RentalOffer::getAccessoryPrice<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#226\">App\\Services\\Offer\\RentalOffer::hasExtraDayCharge<\/a>"],[0,4,"<a href=\"RentalOffer.php.html#235\">App\\Services\\Offer\\RentalOffer::hasExtraMilesCharge<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#250\">App\\Services\\Offer\\RentalOffer::getExtraMilesCharge<\/a>"],[0,4,"<a href=\"RentalOffer.php.html#259\">App\\Services\\Offer\\RentalOffer::hasRemoteLocationPickupCharge<\/a>"],[0,4,"<a href=\"RentalOffer.php.html#277\">App\\Services\\Offer\\RentalOffer::hasRemoteLocationDropoffCharge<\/a>"],[0,7,"<a href=\"RentalOffer.php.html#291\">App\\Services\\Offer\\RentalOffer::getRemoteLocationCharge<\/a>"],[0,4,"<a href=\"RentalOffer.php.html#332\">App\\Services\\Offer\\RentalOffer::hasAfterHoursCharge<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#368\">App\\Services\\Offer\\RentalOffer::getAfterHoursCharge<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#377\">App\\Services\\Offer\\RentalOffer::hasFuelPlanCharge<\/a>"],[0,2,"<a href=\"RentalOffer.php.html#382\">App\\Services\\Offer\\RentalOffer::getFuelPlanCharge<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#390\">App\\Services\\Offer\\RentalOffer::hasAvailability<\/a>"],[0,6,"<a href=\"RentalOffer.php.html#398\">App\\Services\\Offer\\RentalOffer::setAvailability<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#439\">App\\Services\\Offer\\RentalOffer::getTotalDays<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#450\">App\\Services\\Offer\\RentalOffer::getPickupLocation<\/a>"],[0,1,"<a href=\"RentalOffer.php.html#460\">App\\Services\\Offer\\RentalOffer::getDropoffLocation<\/a>"],[0,1,"<a href=\"StaticDiscount.php.html#66\">App\\Services\\Offer\\StaticDiscount::__construct<\/a>"],[0,3,"<a href=\"StaticDiscount.php.html#78\">App\\Services\\Offer\\StaticDiscount::getDiscountAmount<\/a>"],[0,3,"<a href=\"StaticDiscount.php.html#109\">App\\Services\\Offer\\StaticDiscount::getNumberOfDaysDiscountPercentage<\/a>"],[0,5,"<a href=\"StaticDiscount.php.html#135\">App\\Services\\Offer\\StaticDiscount::calculateTotalPrice<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        tailwindcss(),
        laravel({
            input: ['resources/css/frontend.css', 'resources/js/frontend.js'],
            refresh: true,
        }),
    ],
    refresh: [
        'resources/views/**/*.blade.php',
        'app/View/Components/**/*.php',
        'routes/**/*.php',
      ],
});

{"private": true, "scripts": {"dev": "npm-run-all --parallel mix vite", "build": "npm-run-all mix-build vite-build", "mix": "mix watch", "mix-build": "mix --production", "vite": "vite", "vite-build": "vite build", "tailwind": "tailwindcss"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "axios": "^1.8.4", "bootstrap-sass": "^3.4.1", "cross-env": "^5.2.1", "jquery": "^3.5.1", "jquery-ui": "^1.12.1", "laravel-mix": "^6.0.49", "laravel-vite-plugin": "^1.2.0", "lodash": "^4.17.15", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "resolve-url-loader": "^5.0.0", "sass": "^1.56.2", "sass-loader": "^16.0.5", "vite": "^6.3.0", "vue": "^3.5.13", "webpack-dev-server": "^5.2.1"}, "dependencies": {"@alpinejs/collapse": "^3.14.9", "@tailwindcss/vite": "^4.1.4", "autoprefixer": "^10.4.21", "flatpickr": "^4.6.13", "js-cookie": "^3.0.5", "magnific-popup": "^1.1.0", "node-sass": "^9.0.0", "popper.js": "^1.12.9", "slick-carousel": "^1.8.1", "swiper": "^11.2.8", "tailwindcss": "^4.1.4"}}